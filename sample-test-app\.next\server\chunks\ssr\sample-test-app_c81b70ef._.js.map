{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/openai-testing-agent-demo/sample-test-app/lib/styleService.ts"], "sourcesContent": ["export async function fetchAllStyles() {\r\n  const res = await fetch(\"/api/store/styles\");\r\n  if (!res.ok) {\r\n    throw new Error(\"Styles not found\");\r\n  }\r\n  // The API returns a raw array of styles\r\n  const data: unknown[] = await res.json();\r\n  return data;\r\n}\r\n\r\nexport async function fetchStyleById(id: number) {\r\n  const res = await fetch(`/api/store/styles/${id}`);\r\n  if (!res.ok) {\r\n    throw new Error(\"Style not found\");\r\n  }\r\n  const data: { style: unknown } = await res.json();\r\n  return data.style;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAO,eAAe;IACpB,MAAM,MAAM,MAAM,MAAM;IACxB,IAAI,CAAC,IAAI,EAAE,EAAE;QACX,MAAM,IAAI,MAAM;IAClB;IACA,wCAAwC;IACxC,MAAM,OAAkB,MAAM,IAAI,IAAI;IACtC,OAAO;AACT;AAEO,eAAe,eAAe,EAAU;IAC7C,MAAM,MAAM,MAAM,MAAM,CAAC,kBAAkB,EAAE,IAAI;IACjD,IAAI,CAAC,IAAI,EAAE,EAAE;QACX,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,OAA2B,MAAM,IAAI,IAAI;IAC/C,OAAO,KAAK,KAAK;AACnB", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/openai-testing-agent-demo/sample-test-app/stores/stylesStore.ts"], "sourcesContent": ["// src/store/stylesStore.ts\r\nimport { create } from \"zustand\";\r\nimport { fetchAllStyles } from \"@/lib/styleService\";\r\n\r\nexport interface StyleItem {\r\n  id: number;\r\n  gender: string;\r\n  masterCategory: string;\r\n  subCategory: string;\r\n  articleType: string;\r\n  baseColour: string;\r\n  season: string;\r\n  year: number;\r\n  usage: string;\r\n  productDisplayName: string;\r\n  imageURL: string;\r\n  priceUSD?: number;\r\n}\r\n\r\ninterface StylesState {\r\n  data: StyleItem[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  fetchStyles: () => Promise<void>;\r\n}\r\n\r\nexport const useStylesStore = create<StylesState>((set) => ({\r\n  data: [],\r\n  loading: false,\r\n  error: null,\r\n  fetchStyles: async () => {\r\n    set({ loading: true, error: null });\r\n    try {\r\n      const styles = await fetchAllStyles();\r\n      set({ data: styles as StyleItem[], loading: false });\r\n    } catch (e: unknown) {\r\n      const error = e as Error;\r\n      set({ error: error.message ?? \"Failed to load styles\", loading: false });\r\n    }\r\n  },\r\n}));\r\n"], "names": [], "mappings": "AAAA,2BAA2B;;;;AAC3B;AACA;;;AAwBO,MAAM,iBAAiB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAe,CAAC,MAAQ,CAAC;QAC1D,MAAM,EAAE;QACR,SAAS;QACT,OAAO;QACP,aAAa;YACX,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YACjC,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,4IAAA,CAAA,iBAAc,AAAD;gBAClC,IAAI;oBAAE,MAAM;oBAAuB,SAAS;gBAAM;YACpD,EAAE,OAAO,GAAY;gBACnB,MAAM,QAAQ;gBACd,IAAI;oBAAE,OAAO,MAAM,OAAO,IAAI;oBAAyB,SAAS;gBAAM;YACxE;QACF;IACF,CAAC", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/openai-testing-agent-demo/sample-test-app/components/ItemCard.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nimport { useCartStore } from \"@/stores/cartStore\";\r\nimport { Minus, Plus } from \"lucide-react\";\r\nimport { ShoppingCart } from \"lucide-react\";\r\n\r\ninterface ItemCardProps {\r\n  item: {\r\n    id: number;\r\n    productDisplayName: string;\r\n    baseColour: string;\r\n    priceUSD?: number;\r\n    imageURL: string;\r\n  };\r\n}\r\n\r\nconst ItemCard: React.FC<ItemCardProps> = ({ item }) => {\r\n  const addItem = useCartStore((s) => s.addItem);\r\n  const increment = useCartStore((s) => s.incrementItem);\r\n  const decrement = useCartStore((s) => s.decrementItem);\r\n  const quantity = useCartStore(\r\n    (s) => s.items.find((i) => i.id === item.id)?.quantity || 0\r\n  );\r\n\r\n  const handleAddToCart = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\r\n    e.stopPropagation();\r\n    addItem(item.id);\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex flex-col h-full cursor-pointer bg-stone-100 p-4 rounded-sm\">\r\n      {/* Product image + title */}\r\n      <div className=\"flex flex-col items-center flex-grow\">\r\n        <img\r\n          src={item.imageURL}\r\n          alt={item.productDisplayName}\r\n          className=\"w-full object-cover\"\r\n        />\r\n        <div className=\"w-full m-4\">\r\n          <h3 className=\"font-medium text-sm text-stone-800 line-clamp-2\">\r\n            {item.productDisplayName}\r\n          </h3>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"w-full py-2 flex items-center justify-between\">\r\n        <span className=\"text-sm font-medium text-stone-900\">\r\n          {`$${item.priceUSD ?? \"N/A\"}`}\r\n        </span>\r\n        {quantity > 0 ? (\r\n          <div className=\"flex items-center md:gap-1\">\r\n            <div\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                decrement(item.id);\r\n              }}\r\n              className=\"px-2 py-1 text-xs cursor-pointer\"\r\n            >\r\n              <Minus className=\"w-3 h-3\" />\r\n            </div>\r\n            <span className=\"text-sm w-4 text-center\">{quantity}</span>\r\n            <div\r\n              onClick={(e) => {\r\n                e.stopPropagation();\r\n                increment(item.id);\r\n              }}\r\n              className=\"px-2 py-1 text-xs cursor-pointer\"\r\n            >\r\n              <Plus className=\"w-3 h-3\" />\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div\r\n            onClick={(e) => {\r\n              e.stopPropagation();\r\n              handleAddToCart(e);\r\n            }}\r\n            className=\"text-[10px] md:text-xs font-medium whitespace-nowrap text-white bg-stone-900 px-3 py-1.5 rounded-sm cursor-pointer hover:bg-stone-700 transition-colors duration-200\"\r\n          >\r\n            <ShoppingCart className=\"w-4 h-4\" />\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ItemCard;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;;;AAYA,MAAM,WAAoC,CAAC,EAAE,IAAI,EAAE;IACjD,MAAM,UAAU,CAAA,GAAA,4IAAA,CAAA,eAAY,AAAD,EAAE,CAAC,IAAM,EAAE,OAAO;IAC7C,MAAM,YAAY,CAAA,GAAA,4IAAA,CAAA,eAAY,AAAD,EAAE,CAAC,IAAM,EAAE,aAAa;IACrD,MAAM,YAAY,CAAA,GAAA,4IAAA,CAAA,eAAY,AAAD,EAAE,CAAC,IAAM,EAAE,aAAa;IACrD,MAAM,WAAW,CAAA,GAAA,4IAAA,CAAA,eAAY,AAAD,EAC1B,CAAC,IAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK,KAAK,EAAE,GAAG,YAAY;IAG5D,MAAM,kBAAkB,CAAC;QACvB,EAAE,eAAe;QACjB,QAAQ,KAAK,EAAE;IACjB;IAEA,qBACE,uQAAC;QAAI,WAAU;;0BAEb,uQAAC;gBAAI,WAAU;;kCACb,uQAAC;wBACC,KAAK,KAAK,QAAQ;wBAClB,KAAK,KAAK,kBAAkB;wBAC5B,WAAU;;;;;;kCAEZ,uQAAC;wBAAI,WAAU;kCACb,cAAA,uQAAC;4BAAG,WAAU;sCACX,KAAK,kBAAkB;;;;;;;;;;;;;;;;;0BAK9B,uQAAC;gBAAI,WAAU;;kCACb,uQAAC;wBAAK,WAAU;kCACb,CAAC,CAAC,EAAE,KAAK,QAAQ,IAAI,OAAO;;;;;;oBAE9B,WAAW,kBACV,uQAAC;wBAAI,WAAU;;0CACb,uQAAC;gCACC,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,UAAU,KAAK,EAAE;gCACnB;gCACA,WAAU;0CAEV,cAAA,uQAAC,6NAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,uQAAC;gCAAK,WAAU;0CAA2B;;;;;;0CAC3C,uQAAC;gCACC,SAAS,CAAC;oCACR,EAAE,eAAe;oCACjB,UAAU,KAAK,EAAE;gCACnB;gCACA,WAAU;0CAEV,cAAA,uQAAC,2NAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;6CAIpB,uQAAC;wBACC,SAAS,CAAC;4BACR,EAAE,eAAe;4BACjB,gBAAgB;wBAClB;wBACA,WAAU;kCAEV,cAAA,uQAAC,+OAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMpC;uCAEe", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/openai-testing-agent-demo/sample-test-app/app/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { useStylesStore } from \"@/stores/stylesStore\";\r\nimport StyleCard from \"@/components/ItemCard\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\n\r\nfunction HomeCallout({\r\n  title,\r\n  description,\r\n  path,\r\n}: {\r\n  title: string;\r\n  description: string;\r\n  path: string;\r\n}) {\r\n  return (\r\n    <Link\r\n      href={path}\r\n      className=\"p-8 bg-stone-200 h-72 rounded-sm hover:bg-stone-200 hover:shadow-sm hover:translate-y-[-2px] transition-all duration-300\"\r\n    >\r\n      <h3 className=\"text-xl font-semibold mb-2\">{title}</h3>\r\n      <p className=\"text-stone-600\">{description}</p>\r\n    </Link>\r\n  );\r\n}\r\n\r\nexport default function HomePage() {\r\n  const { data, loading, fetchStyles } = useStylesStore();\r\n  useEffect(() => {\r\n    fetchStyles();\r\n  }, [fetchStyles]);\r\n  const justIn = data.slice(0, 4);\r\n\r\n  const callouts = [\r\n    {\r\n      title: \"Clothes\",\r\n      description: \"Shop the latest apparel\",\r\n      path: \"/shop/clothes\",\r\n    },\r\n    {\r\n      title: \"Shoes\",\r\n      description: \"Browse our footwear\",\r\n      path: \"/shop/shoes\",\r\n    },\r\n    {\r\n      title: \"Accessories\",\r\n      description: \"View our accessories\",\r\n      path: \"/shop/accessories\",\r\n    },\r\n  ];\r\n\r\n  return (\r\n    <div className=\"bg-white\">\r\n      {/* Hero Banner */}\r\n      <Image\r\n        src=\"/cover.webp\"\r\n        alt=\"Hero Banner\"\r\n        className=\"w-full object-cover\"\r\n        width={1500}\r\n        height={260}\r\n      />\r\n\r\n      {/* Just In Section */}\r\n      <section className=\"py-12 px-6 max-w-7xl mx-auto\">\r\n        <div className=\"flex items-center justify-between mb-6\">\r\n          <h2 className=\"text-3xl font-semibold\">Just In</h2>\r\n          <Link\r\n            href=\"/shop/just-in\"\r\n            className=\"text-stone-700 flex items-center group\"\r\n          >\r\n            View all\r\n            <ArrowRight className=\"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300\" />\r\n          </Link>\r\n        </div>\r\n        {loading ? (\r\n          <p>Loading...</p>\r\n        ) : (\r\n          <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6\">\r\n            {justIn.map((item) => (\r\n              <StyleCard key={item.id} item={item} />\r\n            ))}\r\n          </div>\r\n        )}\r\n      </section>\r\n\r\n      {/* Offers and Categories */}\r\n      <section className=\"py-12 px-6\">\r\n        <div className=\"max-w-7xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\r\n          {callouts.map((callout) => (\r\n            <HomeCallout\r\n              key={callout.title}\r\n              title={callout.title}\r\n              description={callout.description}\r\n              path={callout.path}\r\n            />\r\n          ))}\r\n          <Link\r\n            href=\"/shop/offers\"\r\n            className=\"col-span-2 p-8 bg-stone-500 h-72 rounded-sm\r\n            hover:bg-stone-600 hover:shadow-sm\r\n            hover:-translate-y-1 transition-all duration-300\"\r\n          >\r\n            <h3 className=\"text-xl font-semibold mb-2 text-stone-50\">Offers</h3>\r\n            <p className=\"text-stone-200\">Discover our best deals</p>\r\n          </Link>\r\n        </div>\r\n      </section>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAQA,SAAS,YAAY,EACnB,KAAK,EACL,WAAW,EACX,IAAI,EAKL;IACC,qBACE,uQAAC,qLAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;;0BAEV,uQAAC;gBAAG,WAAU;0BAA8B;;;;;;0BAC5C,uQAAC;gBAAE,WAAU;0BAAkB;;;;;;;;;;;;AAGrC;AAEe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,iBAAc,AAAD;IACpD,CAAA,GAAA,8NAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAY;IAChB,MAAM,SAAS,KAAK,KAAK,CAAC,GAAG;IAE7B,MAAM,WAAW;QACf;YACE,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,qBACE,uQAAC;QAAI,WAAU;;0BAEb,uQAAC,sJAAA,CAAA,UAAK;gBACJ,KAAI;gBACJ,KAAI;gBACJ,WAAU;gBACV,OAAO;gBACP,QAAQ;;;;;;0BAIV,uQAAC;gBAAQ,WAAU;;kCACjB,uQAAC;wBAAI,WAAU;;0CACb,uQAAC;gCAAG,WAAU;0CAAyB;;;;;;0CACvC,uQAAC,qLAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;oCACX;kDAEC,uQAAC,2OAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;oBAGzB,wBACC,uQAAC;kCAAE;;;;;6CAEH,uQAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,qBACX,uQAAC,gJAAA,CAAA,UAAS;gCAAe,MAAM;+BAAf,KAAK,EAAE;;;;;;;;;;;;;;;;0BAO/B,uQAAC;gBAAQ,WAAU;0BACjB,cAAA,uQAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,uQAAC;gCAEC,OAAO,QAAQ,KAAK;gCACpB,aAAa,QAAQ,WAAW;gCAChC,MAAM,QAAQ,IAAI;+BAHb,QAAQ,KAAK;;;;;sCAMtB,uQAAC,qLAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAIV,uQAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,uQAAC;oCAAE,WAAU;8CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1C", "debugId": null}}]}