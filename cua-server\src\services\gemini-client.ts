import { GoogleGenerativeAI, GenerativeModel, Part, Content, Tool, FunctionCall } from "@google/generative-ai";
import logger from "../utils/logger";

// Initialize Gemini client
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || "");

// Environment specific instructions for the CUA model e.g., MacOS specific actions CMD+A vs CTRL+A
const envInstructions = process.env.ENV_SPECIFIC_INSTRUCTIONS || "";

// Helper: Read display dimensions from env
const displayWidth: number = parseInt(process.env.DISPLAY_WIDTH || "1024", 10);
const displayHeight: number = parseInt(process.env.DISPLAY_HEIGHT || "768", 10);

const cuaPrompt = `You are a testing agent. You will be given a list of instructions with steps to test a web application. 
You will need to navigate the web application and perform the actions described in the instructions.
Try to accomplish the provided task in the simplest way possible.
Once you believe your are done with all the tasks required or you are blocked and cannot progress
(for example, you have tried multiple times to acommplish a task but keep getting errors or blocked),
use the mark_done tool to let the user know you have finished the tasks.
You do not need to authenticate on user's behalf, the user will authenticate and your flow starts after that.`;

// Define tools for Gemini (similar to OpenAI's computer use tools)
const tools: Tool[] = [
  {
    functionDeclarations: [
      {
        name: "computer_use",
        description: "Use computer to interact with the screen through clicks, typing, and other actions",
        parameters: {
          type: "object",
          properties: {
            action: {
              type: "string",
              enum: ["click", "type", "key", "screenshot", "scroll"],
              description: "The action to perform"
            },
            coordinate: {
              type: "array",
              items: { type: "number" },
              description: "X, Y coordinates for click actions"
            },
            text: {
              type: "string",
              description: "Text to type"
            },
            key: {
              type: "string",
              description: "Key to press (e.g., 'Enter', 'Tab', 'Escape')"
            },
            scroll_direction: {
              type: "string",
              enum: ["up", "down", "left", "right"],
              description: "Direction to scroll"
            }
          },
          required: ["action"]
        }
      },
      {
        name: "mark_done",
        description: "Use this tool to let the user know you have finished the tasks.",
        parameters: {
          type: "object",
          properties: {},
          required: []
        }
      }
    ]
  }
];

interface GeminiResponse {
  id: string;
  output: Array<any>;
  text?: string;
  functionCalls?: FunctionCall[];
}

export interface ModelInput {
  screenshotBase64: string;
  previousResponseId?: string;
  lastCallId?: string;
}

// Get the appropriate model based on configuration
function getModel(modelName?: string): GenerativeModel {
  const defaultModel = process.env.GEMINI_MODEL || "gemini-2.5-flash-preview-05-20";
  const selectedModel = modelName || defaultModel;
  
  logger.trace(`Using Gemini model: ${selectedModel}`);
  return genAI.getGenerativeModel({ model: selectedModel });
}

// Helper to construct and send a request to the Gemini model
async function callGeminiModel(
  input: Content[], 
  modelName?: string,
  previousResponseId?: string
): Promise<GeminiResponse> {
  logger.trace("Sending request body to Gemini model...");

  const model = getModel(modelName);
  
  try {
    const result = await model.generateContent({
      contents: input,
      tools: tools,
      generationConfig: {
        temperature: 0.1, // Low temperature for more deterministic responses
      }
    });

    const response = await result.response;
    const text = response.text();
    const functionCalls = response.functionCalls();

    logger.trace("Received response from Gemini model.");
    
    return {
      id: `gemini-${Date.now()}`, // Generate a simple ID
      output: [response],
      text: text,
      functionCalls: functionCalls || []
    };
  } catch (error) {
    logger.error("Error calling Gemini model:", error);
    throw error;
  }
}

/**
 * Sends input (or screenshot output) to the Gemini model.
 * If no lastCallId is provided, it sends an initial query.
 */
export async function sendInputToModel(
  { screenshotBase64, previousResponseId, lastCallId }: ModelInput,
  userMessage?: string,
  modelName?: string
): Promise<GeminiResponse> {
  logger.trace("Building image input for Gemini model...");
  const input: Content[] = [];

  if (userMessage) {
    input.push({
      role: "user",
      parts: [{ text: userMessage }]
    });
  }

  if (lastCallId && screenshotBase64) {
    // This is a follow-up call with a screenshot
    logger.trace(`Adding screenshot to the input with the call ID: ${lastCallId}`);
    
    const imagePart: Part = {
      inlineData: {
        mimeType: "image/png",
        data: screenshotBase64
      }
    };

    if (input.length > 0) {
      // Add image to existing user message
      input[input.length - 1].parts.push(imagePart);
    } else {
      // Create new user message with image
      input.push({
        role: "user",
        parts: [imagePart]
      });
    }
  }

  return callGeminiModel(input, modelName, previousResponseId);
}

export async function sendFunctionCallOutput(
  callId: string,
  previousResponseId: string,
  outputObj: object = {},
  modelName?: string
): Promise<GeminiResponse> {
  const input: Content[] = [
    {
      role: "user",
      parts: [
        {
          functionResponse: {
            name: callId,
            response: outputObj
          }
        }
      ]
    }
  ];

  return callGeminiModel(input, modelName, previousResponseId);
}

export async function setupGeminiModel(
  systemPrompt: string, 
  userInfo: string,
  modelName?: string
): Promise<GeminiResponse> {
  logger.trace("Setting up Gemini model...");
  
  const gemini_initiation_prompt = `${cuaPrompt}
      ${
        envInstructions
          ? "Environment specific instructions: " + envInstructions
          : ""
      }
      `;

  logger.trace(`Gemini system prompt: ${gemini_initiation_prompt}`);

  const input: Content[] = [
    {
      role: "user",
      parts: [
        { 
          text: `${gemini_initiation_prompt}\n\nINSTRUCTIONS:\n${systemPrompt}\n\nUSER INFO:\n${userInfo}` 
        }
      ]
    }
  ];

  return callGeminiModel(input, modelName);
}

// Export model names for configuration
export const GEMINI_MODELS = {
  GEMINI_2_5_PRO: "gemini-2.5-pro-preview-06-05",
  GEMINI_2_5_FLASH: "gemini-2.5-flash-preview-05-20",
  GEMINI_2_0_FLASH: "gemini-2.0-flash"
} as const;

export type GeminiModelName = typeof GEMINI_MODELS[keyof typeof GEMINI_MODELS];
