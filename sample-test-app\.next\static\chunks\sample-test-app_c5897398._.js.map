{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/openai-testing-agent-demo/sample-test-app/stores/cartStore.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\n\r\ninterface CartItem {\r\n  id: number;\r\n  quantity: number;\r\n}\r\n\r\ninterface CartState {\r\n  items: CartItem[];\r\n\r\n  /**\r\n   * Add an item to the cart.\r\n   * @param id        Product ID\r\n   * @param quantity  How many to add (default = 1)\r\n   */\r\n  addItem: (id: number, quantity?: number) => void;\r\n\r\n  /** Increase quantity by 1 */\r\n  incrementItem: (id: number) => void;\r\n\r\n  /** Decrease quantity by 1 (remove if it hits zero) */\r\n  decrementItem: (id: number) => void;\r\n\r\n  /** Set item quantity explicitly (remove if quantity <= 0) */\r\n  setItemQuantity: (id: number, quantity: number) => void;\r\n\r\n  /** Remove *all* copies of the given product from the cart */\r\n  removeItem: (id: number) => void;\r\n\r\n  /** Empty the entire cart */\r\n  clear: () => void;\r\n}\r\n\r\nexport const useCartStore = create<CartState>((set) => ({\r\n  items: [],\r\n\r\n  addItem: (id, quantity = 1) =>\r\n    set((state) => {\r\n      const existing = state.items.find((i) => i.id === id);\r\n\r\n      if (existing) {\r\n        return {\r\n          items: state.items.map((i) =>\r\n            i.id === id ? { ...i, quantity: i.quantity + quantity } : i\r\n          ),\r\n        };\r\n      }\r\n\r\n      return { items: [...state.items, { id, quantity }] };\r\n    }),\r\n\r\n  incrementItem: (id) =>\r\n    set((state) => ({\r\n      items: state.items.map((i) =>\r\n        i.id === id ? { ...i, quantity: i.quantity + 1 } : i\r\n      ),\r\n    })),\r\n\r\n  decrementItem: (id) =>\r\n    set((state) => {\r\n      const existing = state.items.find((i) => i.id === id);\r\n      if (!existing) return state;\r\n      if (existing.quantity <= 1) {\r\n        return { items: state.items.filter((i) => i.id !== id) };\r\n      }\r\n      return {\r\n        items: state.items.map((i) =>\r\n          i.id === id ? { ...i, quantity: i.quantity - 1 } : i\r\n        ),\r\n      };\r\n    }),\r\n\r\n  setItemQuantity: (id, quantity) =>\r\n    set((state) => {\r\n      if (quantity <= 0)\r\n        return { items: state.items.filter((i) => i.id !== id) };\r\n      const existing = state.items.find((i) => i.id === id);\r\n      if (existing) {\r\n        return {\r\n          items: state.items.map((i) => (i.id === id ? { ...i, quantity } : i)),\r\n        };\r\n      }\r\n      return { items: [...state.items, { id, quantity }] };\r\n    }),\r\n\r\n  removeItem: (id) =>\r\n    set((state) => ({ items: state.items.filter((i) => i.id !== id) })),\r\n\r\n  clear: () => set({ items: [] }),\r\n}));\r\n"], "names": [], "mappings": ";;;AAAA;;AAiCO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAa,CAAC,MAAQ,CAAC;QACtD,OAAO,EAAE;QAET,SAAS,CAAC,IAAI,WAAW,CAAC,GACxB,IAAI,CAAC;gBACH,MAAM,WAAW,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAElD,IAAI,UAAU;oBACZ,OAAO;wBACL,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,IACtB,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE,UAAU,EAAE,QAAQ,GAAG;4BAAS,IAAI;oBAE9D;gBACF;gBAEA,OAAO;oBAAE,OAAO;2BAAI,MAAM,KAAK;wBAAE;4BAAE;4BAAI;wBAAS;qBAAE;gBAAC;YACrD;QAEF,eAAe,CAAC,KACd,IAAI,CAAC,QAAU,CAAC;oBACd,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,IACtB,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,UAAU,EAAE,QAAQ,GAAG;wBAAE,IAAI;gBAEvD,CAAC;QAEH,eAAe,CAAC,KACd,IAAI,CAAC;gBACH,MAAM,WAAW,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAClD,IAAI,CAAC,UAAU,OAAO;gBACtB,IAAI,SAAS,QAAQ,IAAI,GAAG;oBAC1B,OAAO;wBAAE,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAAI;gBACzD;gBACA,OAAO;oBACL,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,IACtB,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,UAAU,EAAE,QAAQ,GAAG;wBAAE,IAAI;gBAEvD;YACF;QAEF,iBAAiB,CAAC,IAAI,WACpB,IAAI,CAAC;gBACH,IAAI,YAAY,GACd,OAAO;oBAAE,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAAI;gBACzD,MAAM,WAAW,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAClD,IAAI,UAAU;oBACZ,OAAO;wBACL,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,IAAO,EAAE,EAAE,KAAK,KAAK;gCAAE,GAAG,CAAC;gCAAE;4BAAS,IAAI;oBACpE;gBACF;gBACA,OAAO;oBAAE,OAAO;2BAAI,MAAM,KAAK;wBAAE;4BAAE;4BAAI;wBAAS;qBAAE;gBAAC;YACrD;QAEF,YAAY,CAAC,KACX,IAAI,CAAC,QAAU,CAAC;oBAAE,OAAO,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAAI,CAAC;QAEnE,OAAO,IAAM,IAAI;gBAAE,OAAO,EAAE;YAAC;IAC/B,CAAC", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/openai-testing-agent-demo/sample-test-app/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport Link from \"next/link\";\r\nimport { Shopping<PERSON><PERSON>, Heart } from \"lucide-react\";\r\nimport { useCartStore } from \"@/stores/cartStore\";\r\n\r\nexport default function Navbar() {\r\n  const totalQty = useCartStore((s) =>\r\n    s.items.reduce((acc, i) => acc + i.quantity, 0)\r\n  );\r\n  // Link to shop categories under /shop/[category]\r\n  const navItems = [\r\n    { label: \"Just In\", href: \"/shop/just-in\" },\r\n    { label: \"Clothes\", href: \"/shop/clothes\" },\r\n    { label: \"Shoes\", href: \"/shop/shoes\" },\r\n    { label: \"Accessories\", href: \"/shop/accessories\" },\r\n    { label: \"Offers\", href: \"/shop/offers\" },\r\n  ];\r\n\r\n  return (\r\n    <nav className=\"w-full border-b border-stone-200 py-2\">\r\n      <div className=\"max-w-7xl mx-auto flex items-center justify-between px-4 py-3\">\r\n        <div className=\"text-2xl font-bold mb-0.5\">\r\n          <Link href=\"/\">THE STORE</Link>\r\n        </div>\r\n        <div className=\"hidden md:flex md:flex-1 justify-center space-x-6\">\r\n          {navItems.map((item) => (\r\n            <Link\r\n              key={item.href}\r\n              href={item.href}\r\n              className=\"text-stone-600 hover:text-stone-900\"\r\n            >\r\n              {item.label}\r\n            </Link>\r\n          ))}\r\n        </div>\r\n        <div className=\"flex items-center justify-end space-x-4\">\r\n          <Link\r\n            href=\"/favorites\"\r\n            className=\"relative text-stone-700 hover:text-stone-900\"\r\n          >\r\n            <Heart className=\"w-5 h-5 text-stone-700 hover:text-red-600 transition-colors duration-200\" />\r\n          </Link>\r\n          <Link\r\n            href=\"/cart\"\r\n            className=\"relative text-stone-700 hover:text-stone-900\"\r\n          >\r\n            <ShoppingCart className=\"w-5 h-5 text-stone-700 hover:text-stone-900 transition-colors duration-200\" />\r\n            {totalQty > 0 && (\r\n              <span className=\"absolute -top-1 -right-2 flex h-4 w-4 items-center justify-center rounded-full bg-red-600 text-xs text-white\">\r\n                {totalQty}\r\n              </span>\r\n            )}\r\n          </Link>\r\n        </div>\r\n      </div>\r\n    </nav>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,WAAW,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD;yCAAE,CAAC,IAC7B,EAAE,KAAK,CAAC,MAAM;iDAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ;gDAAE;;IAE/C,iDAAiD;IACjD,MAAM,WAAW;QACf;YAAE,OAAO;YAAW,MAAM;QAAgB;QAC1C;YAAE,OAAO;YAAW,MAAM;QAAgB;QAC1C;YAAE,OAAO;YAAS,MAAM;QAAc;QACtC;YAAE,OAAO;YAAe,MAAM;QAAoB;QAClD;YAAE,OAAO;YAAU,MAAM;QAAe;KACzC;IAED,qBACE,sNAAC;QAAI,WAAU;kBACb,cAAA,sNAAC;YAAI,WAAU;;8BACb,sNAAC;oBAAI,WAAU;8BACb,cAAA,sNAAC,wLAAA,CAAA,UAAI;wBAAC,MAAK;kCAAI;;;;;;;;;;;8BAEjB,sNAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,sNAAC,wLAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAU;sCAET,KAAK,KAAK;2BAJN,KAAK,IAAI;;;;;;;;;;8BAQpB,sNAAC;oBAAI,WAAU;;sCACb,sNAAC,wLAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;sCAEV,cAAA,sNAAC,gOAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,sNAAC,wLAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAU;;8CAEV,sNAAC,kPAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCACvB,WAAW,mBACV,sNAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;GApDwB;;QACL,+IAAA,CAAA,eAAY;;;KADP", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/openai-testing-agent-demo/sample-test-app/stores/authStore.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nimport { persist } from \"zustand/middleware\";\r\n\r\ninterface AuthState {\r\n  token: string | null;\r\n  setToken: (token: string | null) => void;\r\n  logout: () => void;\r\n}\r\n\r\nexport const useAuthStore = create<AuthState>()(\r\n  persist(\r\n    (set) => ({\r\n      token: null,\r\n      setToken: (token) => set({ token }),\r\n      logout: () => set({ token: null }),\r\n    }),\r\n    { name: \"auth\" }\r\n  )\r\n);"], "names": [], "mappings": ";;;AAAA;AACA;;;AAQO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,MAAQ,CAAC;QACR,OAAO;QACP,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,QAAQ,IAAM,IAAI;gBAAE,OAAO;YAAK;IAClC,CAAC,GACD;IAAE,MAAM;AAAO", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/code/openai-testing-agent-demo/sample-test-app/components/AuthGuard.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { ReactNode, useEffect, useState } from \"react\";\r\nimport { useRouter, usePathname } from \"next/navigation\";\r\nimport { useAuthStore } from \"@/stores/authStore\";\r\n\r\ninterface AuthGuardProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport default function AuthGuard({ children }: AuthGuardProps) {\r\n  const token = useAuthStore((s) => s.token);\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n  const [authorized, setAuthorized] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // Redirect to login if not authenticated and not already on login page\r\n    if (!token && pathname !== \"/login\") {\r\n      router.push(`/login?next=${pathname}`);\r\n    } else {\r\n      setAuthorized(true);\r\n    }\r\n  }, [token, pathname, router]);\r\n\r\n  if (!authorized) {\r\n    return null;\r\n  }\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAUe,SAAS,UAAU,EAAE,QAAQ,EAAkB;;IAC5D,MAAM,QAAQ,CAAA,GAAA,+IAAA,CAAA,eAAY,AAAD;yCAAE,CAAC,IAAM,EAAE,KAAK;;IACzC,MAAM,SAAS,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,sLAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,sLAAA,CAAA,YAAS,AAAD;+BAAE;YACR,uEAAuE;YACvE,IAAI,CAAC,SAAS,aAAa,UAAU;gBACnC,OAAO,IAAI,CAAC,CAAC,YAAY,EAAE,UAAU;YACvC,OAAO;gBACL,cAAc;YAChB;QACF;8BAAG;QAAC;QAAO;QAAU;KAAO;IAE5B,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,qBAAO;kBAAG;;AACZ;GApBwB;;QACR,+IAAA,CAAA,eAAY;QACX,8JAAA,CAAA,YAAS;QACP,8JAAA,CAAA,cAAW;;;KAHN", "debugId": null}}]}