"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/csv-parse";
exports.ids = ["vendor-chunks/csv-parse"];
exports.modules = {

/***/ "(rsc)/./node_modules/csv-parse/lib/api/CsvError.js":
/*!****************************************************!*\
  !*** ./node_modules/csv-parse/lib/api/CsvError.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CsvError: () => (/* binding */ CsvError)\n/* harmony export */ });\nclass CsvError extends Error {\n  constructor(code, message, options, ...contexts) {\n    if (Array.isArray(message)) message = message.join(\" \").trim();\n    super(message);\n    if (Error.captureStackTrace !== undefined) {\n      Error.captureStackTrace(this, CsvError);\n    }\n    this.code = code;\n    for (const context of contexts) {\n      for (const key in context) {\n        const value = context[key];\n        this[key] = Buffer.isBuffer(value)\n          ? value.toString(options.encoding)\n          : value == null\n            ? value\n            : JSON.parse(JSON.stringify(value));\n      }\n    }\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY3N2LXBhcnNlL2xpYi9hcGkvQ3N2RXJyb3IuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9CIiwic291cmNlcyI6WyJEOlxcY29kZVxcb3BlbmFpLXRlc3RpbmctYWdlbnQtZGVtb1xcc2FtcGxlLXRlc3QtYXBwXFxub2RlX21vZHVsZXNcXGNzdi1wYXJzZVxcbGliXFxhcGlcXENzdkVycm9yLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNsYXNzIENzdkVycm9yIGV4dGVuZHMgRXJyb3Ige1xuICBjb25zdHJ1Y3Rvcihjb2RlLCBtZXNzYWdlLCBvcHRpb25zLCAuLi5jb250ZXh0cykge1xuICAgIGlmIChBcnJheS5pc0FycmF5KG1lc3NhZ2UpKSBtZXNzYWdlID0gbWVzc2FnZS5qb2luKFwiIFwiKS50cmltKCk7XG4gICAgc3VwZXIobWVzc2FnZSk7XG4gICAgaWYgKEVycm9yLmNhcHR1cmVTdGFja1RyYWNlICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIEVycm9yLmNhcHR1cmVTdGFja1RyYWNlKHRoaXMsIENzdkVycm9yKTtcbiAgICB9XG4gICAgdGhpcy5jb2RlID0gY29kZTtcbiAgICBmb3IgKGNvbnN0IGNvbnRleHQgb2YgY29udGV4dHMpIHtcbiAgICAgIGZvciAoY29uc3Qga2V5IGluIGNvbnRleHQpIHtcbiAgICAgICAgY29uc3QgdmFsdWUgPSBjb250ZXh0W2tleV07XG4gICAgICAgIHRoaXNba2V5XSA9IEJ1ZmZlci5pc0J1ZmZlcih2YWx1ZSlcbiAgICAgICAgICA/IHZhbHVlLnRvU3RyaW5nKG9wdGlvbnMuZW5jb2RpbmcpXG4gICAgICAgICAgOiB2YWx1ZSA9PSBudWxsXG4gICAgICAgICAgICA/IHZhbHVlXG4gICAgICAgICAgICA6IEpTT04ucGFyc2UoSlNPTi5zdHJpbmdpZnkodmFsdWUpKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuZXhwb3J0IHsgQ3N2RXJyb3IgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/csv-parse/lib/api/CsvError.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/csv-parse/lib/api/index.js":
/*!*************************************************!*\
  !*** ./node_modules/csv-parse/lib/api/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CsvError: () => (/* reexport safe */ _CsvError_js__WEBPACK_IMPORTED_MODULE_3__.CsvError),\n/* harmony export */   transform: () => (/* binding */ transform)\n/* harmony export */ });\n/* harmony import */ var _normalize_columns_array_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./normalize_columns_array.js */ \"(rsc)/./node_modules/csv-parse/lib/api/normalize_columns_array.js\");\n/* harmony import */ var _init_state_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./init_state.js */ \"(rsc)/./node_modules/csv-parse/lib/api/init_state.js\");\n/* harmony import */ var _normalize_options_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./normalize_options.js */ \"(rsc)/./node_modules/csv-parse/lib/api/normalize_options.js\");\n/* harmony import */ var _CsvError_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CsvError.js */ \"(rsc)/./node_modules/csv-parse/lib/api/CsvError.js\");\n\n\n\n\n\nconst isRecordEmpty = function (record) {\n  return record.every(\n    (field) =>\n      field == null || (field.toString && field.toString().trim() === \"\"),\n  );\n};\n\nconst cr = 13; // `\\r`, carriage return, 0x0D in hexadécimal, 13 in decimal\nconst nl = 10; // `\\n`, newline, 0x0A in hexadecimal, 10 in decimal\n\nconst boms = {\n  // Note, the following are equals:\n  // Buffer.from(\"\\ufeff\")\n  // Buffer.from([239, 187, 191])\n  // Buffer.from('EFBBBF', 'hex')\n  utf8: Buffer.from([239, 187, 191]),\n  // Note, the following are equals:\n  // Buffer.from \"\\ufeff\", 'utf16le\n  // Buffer.from([255, 254])\n  utf16le: Buffer.from([255, 254]),\n};\n\nconst transform = function (original_options = {}) {\n  const info = {\n    bytes: 0,\n    comment_lines: 0,\n    empty_lines: 0,\n    invalid_field_length: 0,\n    lines: 1,\n    records: 0,\n  };\n  const options = (0,_normalize_options_js__WEBPACK_IMPORTED_MODULE_2__.normalize_options)(original_options);\n  return {\n    info: info,\n    original_options: original_options,\n    options: options,\n    state: (0,_init_state_js__WEBPACK_IMPORTED_MODULE_1__.init_state)(options),\n    __needMoreData: function (i, bufLen, end) {\n      if (end) return false;\n      const { encoding, escape, quote } = this.options;\n      const { quoting, needMoreDataSize, recordDelimiterMaxLength } =\n        this.state;\n      const numOfCharLeft = bufLen - i - 1;\n      const requiredLength = Math.max(\n        needMoreDataSize,\n        // Skip if the remaining buffer smaller than record delimiter\n        // If \"record_delimiter\" is yet to be discovered:\n        // 1. It is equals to `[]` and \"recordDelimiterMaxLength\" equals `0`\n        // 2. We set the length to windows line ending in the current encoding\n        // Note, that encoding is known from user or bom discovery at that point\n        // recordDelimiterMaxLength,\n        recordDelimiterMaxLength === 0\n          ? Buffer.from(\"\\r\\n\", encoding).length\n          : recordDelimiterMaxLength,\n        // Skip if remaining buffer can be an escaped quote\n        quoting ? (escape === null ? 0 : escape.length) + quote.length : 0,\n        // Skip if remaining buffer can be record delimiter following the closing quote\n        quoting ? quote.length + recordDelimiterMaxLength : 0,\n      );\n      return numOfCharLeft < requiredLength;\n    },\n    // Central parser implementation\n    parse: function (nextBuf, end, push, close) {\n      const {\n        bom,\n        comment_no_infix,\n        encoding,\n        from_line,\n        ltrim,\n        max_record_size,\n        raw,\n        relax_quotes,\n        rtrim,\n        skip_empty_lines,\n        to,\n        to_line,\n      } = this.options;\n      let { comment, escape, quote, record_delimiter } = this.options;\n      const { bomSkipped, previousBuf, rawBuffer, escapeIsQuote } = this.state;\n      let buf;\n      if (previousBuf === undefined) {\n        if (nextBuf === undefined) {\n          // Handle empty string\n          close();\n          return;\n        } else {\n          buf = nextBuf;\n        }\n      } else if (previousBuf !== undefined && nextBuf === undefined) {\n        buf = previousBuf;\n      } else {\n        buf = Buffer.concat([previousBuf, nextBuf]);\n      }\n      // Handle UTF BOM\n      if (bomSkipped === false) {\n        if (bom === false) {\n          this.state.bomSkipped = true;\n        } else if (buf.length < 3) {\n          // No enough data\n          if (end === false) {\n            // Wait for more data\n            this.state.previousBuf = buf;\n            return;\n          }\n        } else {\n          for (const encoding in boms) {\n            if (boms[encoding].compare(buf, 0, boms[encoding].length) === 0) {\n              // Skip BOM\n              const bomLength = boms[encoding].length;\n              this.state.bufBytesStart += bomLength;\n              buf = buf.slice(bomLength);\n              // Renormalize original options with the new encoding\n              this.options = (0,_normalize_options_js__WEBPACK_IMPORTED_MODULE_2__.normalize_options)({\n                ...this.original_options,\n                encoding: encoding,\n              });\n              // Options will re-evaluate the Buffer with the new encoding\n              ({ comment, escape, quote } = this.options);\n              break;\n            }\n          }\n          this.state.bomSkipped = true;\n        }\n      }\n      const bufLen = buf.length;\n      let pos;\n      for (pos = 0; pos < bufLen; pos++) {\n        // Ensure we get enough space to look ahead\n        // There should be a way to move this out of the loop\n        if (this.__needMoreData(pos, bufLen, end)) {\n          break;\n        }\n        if (this.state.wasRowDelimiter === true) {\n          this.info.lines++;\n          this.state.wasRowDelimiter = false;\n        }\n        if (to_line !== -1 && this.info.lines > to_line) {\n          this.state.stop = true;\n          close();\n          return;\n        }\n        // Auto discovery of record_delimiter, unix, mac and windows supported\n        if (this.state.quoting === false && record_delimiter.length === 0) {\n          const record_delimiterCount = this.__autoDiscoverRecordDelimiter(\n            buf,\n            pos,\n          );\n          if (record_delimiterCount) {\n            record_delimiter = this.options.record_delimiter;\n          }\n        }\n        const chr = buf[pos];\n        if (raw === true) {\n          rawBuffer.append(chr);\n        }\n        if (\n          (chr === cr || chr === nl) &&\n          this.state.wasRowDelimiter === false\n        ) {\n          this.state.wasRowDelimiter = true;\n        }\n        // Previous char was a valid escape char\n        // treat the current char as a regular char\n        if (this.state.escaping === true) {\n          this.state.escaping = false;\n        } else {\n          // Escape is only active inside quoted fields\n          // We are quoting, the char is an escape chr and there is a chr to escape\n          // if(escape !== null && this.state.quoting === true && chr === escape && pos + 1 < bufLen){\n          if (\n            escape !== null &&\n            this.state.quoting === true &&\n            this.__isEscape(buf, pos, chr) &&\n            pos + escape.length < bufLen\n          ) {\n            if (escapeIsQuote) {\n              if (this.__isQuote(buf, pos + escape.length)) {\n                this.state.escaping = true;\n                pos += escape.length - 1;\n                continue;\n              }\n            } else {\n              this.state.escaping = true;\n              pos += escape.length - 1;\n              continue;\n            }\n          }\n          // Not currently escaping and chr is a quote\n          // TODO: need to compare bytes instead of single char\n          if (this.state.commenting === false && this.__isQuote(buf, pos)) {\n            if (this.state.quoting === true) {\n              const nextChr = buf[pos + quote.length];\n              const isNextChrTrimable =\n                rtrim && this.__isCharTrimable(buf, pos + quote.length);\n              const isNextChrComment =\n                comment !== null &&\n                this.__compareBytes(comment, buf, pos + quote.length, nextChr);\n              const isNextChrDelimiter = this.__isDelimiter(\n                buf,\n                pos + quote.length,\n                nextChr,\n              );\n              const isNextChrRecordDelimiter =\n                record_delimiter.length === 0\n                  ? this.__autoDiscoverRecordDelimiter(buf, pos + quote.length)\n                  : this.__isRecordDelimiter(nextChr, buf, pos + quote.length);\n              // Escape a quote\n              // Treat next char as a regular character\n              if (\n                escape !== null &&\n                this.__isEscape(buf, pos, chr) &&\n                this.__isQuote(buf, pos + escape.length)\n              ) {\n                pos += escape.length - 1;\n              } else if (\n                !nextChr ||\n                isNextChrDelimiter ||\n                isNextChrRecordDelimiter ||\n                isNextChrComment ||\n                isNextChrTrimable\n              ) {\n                this.state.quoting = false;\n                this.state.wasQuoting = true;\n                pos += quote.length - 1;\n                continue;\n              } else if (relax_quotes === false) {\n                const err = this.__error(\n                  new _CsvError_js__WEBPACK_IMPORTED_MODULE_3__.CsvError(\n                    \"CSV_INVALID_CLOSING_QUOTE\",\n                    [\n                      \"Invalid Closing Quote:\",\n                      `got \"${String.fromCharCode(nextChr)}\"`,\n                      `at line ${this.info.lines}`,\n                      \"instead of delimiter, record delimiter, trimable character\",\n                      \"(if activated) or comment\",\n                    ],\n                    this.options,\n                    this.__infoField(),\n                  ),\n                );\n                if (err !== undefined) return err;\n              } else {\n                this.state.quoting = false;\n                this.state.wasQuoting = true;\n                this.state.field.prepend(quote);\n                pos += quote.length - 1;\n              }\n            } else {\n              if (this.state.field.length !== 0) {\n                // In relax_quotes mode, treat opening quote preceded by chrs as regular\n                if (relax_quotes === false) {\n                  const info = this.__infoField();\n                  const bom = Object.keys(boms)\n                    .map((b) =>\n                      boms[b].equals(this.state.field.toString()) ? b : false,\n                    )\n                    .filter(Boolean)[0];\n                  const err = this.__error(\n                    new _CsvError_js__WEBPACK_IMPORTED_MODULE_3__.CsvError(\n                      \"INVALID_OPENING_QUOTE\",\n                      [\n                        \"Invalid Opening Quote:\",\n                        `a quote is found on field ${JSON.stringify(info.column)} at line ${info.lines}, value is ${JSON.stringify(this.state.field.toString(encoding))}`,\n                        bom ? `(${bom} bom)` : undefined,\n                      ],\n                      this.options,\n                      info,\n                      {\n                        field: this.state.field,\n                      },\n                    ),\n                  );\n                  if (err !== undefined) return err;\n                }\n              } else {\n                this.state.quoting = true;\n                pos += quote.length - 1;\n                continue;\n              }\n            }\n          }\n          if (this.state.quoting === false) {\n            const recordDelimiterLength = this.__isRecordDelimiter(\n              chr,\n              buf,\n              pos,\n            );\n            if (recordDelimiterLength !== 0) {\n              // Do not emit comments which take a full line\n              const skipCommentLine =\n                this.state.commenting &&\n                this.state.wasQuoting === false &&\n                this.state.record.length === 0 &&\n                this.state.field.length === 0;\n              if (skipCommentLine) {\n                this.info.comment_lines++;\n                // Skip full comment line\n              } else {\n                // Activate records emition if above from_line\n                if (\n                  this.state.enabled === false &&\n                  this.info.lines +\n                    (this.state.wasRowDelimiter === true ? 1 : 0) >=\n                    from_line\n                ) {\n                  this.state.enabled = true;\n                  this.__resetField();\n                  this.__resetRecord();\n                  pos += recordDelimiterLength - 1;\n                  continue;\n                }\n                // Skip if line is empty and skip_empty_lines activated\n                if (\n                  skip_empty_lines === true &&\n                  this.state.wasQuoting === false &&\n                  this.state.record.length === 0 &&\n                  this.state.field.length === 0\n                ) {\n                  this.info.empty_lines++;\n                  pos += recordDelimiterLength - 1;\n                  continue;\n                }\n                this.info.bytes = this.state.bufBytesStart + pos;\n                const errField = this.__onField();\n                if (errField !== undefined) return errField;\n                this.info.bytes =\n                  this.state.bufBytesStart + pos + recordDelimiterLength;\n                const errRecord = this.__onRecord(push);\n                if (errRecord !== undefined) return errRecord;\n                if (to !== -1 && this.info.records >= to) {\n                  this.state.stop = true;\n                  close();\n                  return;\n                }\n              }\n              this.state.commenting = false;\n              pos += recordDelimiterLength - 1;\n              continue;\n            }\n            if (this.state.commenting) {\n              continue;\n            }\n            if (\n              comment !== null &&\n              (comment_no_infix === false ||\n                (this.state.record.length === 0 &&\n                  this.state.field.length === 0))\n            ) {\n              const commentCount = this.__compareBytes(comment, buf, pos, chr);\n              if (commentCount !== 0) {\n                this.state.commenting = true;\n                continue;\n              }\n            }\n            const delimiterLength = this.__isDelimiter(buf, pos, chr);\n            if (delimiterLength !== 0) {\n              this.info.bytes = this.state.bufBytesStart + pos;\n              const errField = this.__onField();\n              if (errField !== undefined) return errField;\n              pos += delimiterLength - 1;\n              continue;\n            }\n          }\n        }\n        if (this.state.commenting === false) {\n          if (\n            max_record_size !== 0 &&\n            this.state.record_length + this.state.field.length > max_record_size\n          ) {\n            return this.__error(\n              new _CsvError_js__WEBPACK_IMPORTED_MODULE_3__.CsvError(\n                \"CSV_MAX_RECORD_SIZE\",\n                [\n                  \"Max Record Size:\",\n                  \"record exceed the maximum number of tolerated bytes\",\n                  `of ${max_record_size}`,\n                  `at line ${this.info.lines}`,\n                ],\n                this.options,\n                this.__infoField(),\n              ),\n            );\n          }\n        }\n        const lappend =\n          ltrim === false ||\n          this.state.quoting === true ||\n          this.state.field.length !== 0 ||\n          !this.__isCharTrimable(buf, pos);\n        // rtrim in non quoting is handle in __onField\n        const rappend = rtrim === false || this.state.wasQuoting === false;\n        if (lappend === true && rappend === true) {\n          this.state.field.append(chr);\n        } else if (rtrim === true && !this.__isCharTrimable(buf, pos)) {\n          return this.__error(\n            new _CsvError_js__WEBPACK_IMPORTED_MODULE_3__.CsvError(\n              \"CSV_NON_TRIMABLE_CHAR_AFTER_CLOSING_QUOTE\",\n              [\n                \"Invalid Closing Quote:\",\n                \"found non trimable byte after quote\",\n                `at line ${this.info.lines}`,\n              ],\n              this.options,\n              this.__infoField(),\n            ),\n          );\n        } else {\n          if (lappend === false) {\n            pos += this.__isCharTrimable(buf, pos) - 1;\n          }\n          continue;\n        }\n      }\n      if (end === true) {\n        // Ensure we are not ending in a quoting state\n        if (this.state.quoting === true) {\n          const err = this.__error(\n            new _CsvError_js__WEBPACK_IMPORTED_MODULE_3__.CsvError(\n              \"CSV_QUOTE_NOT_CLOSED\",\n              [\n                \"Quote Not Closed:\",\n                `the parsing is finished with an opening quote at line ${this.info.lines}`,\n              ],\n              this.options,\n              this.__infoField(),\n            ),\n          );\n          if (err !== undefined) return err;\n        } else {\n          // Skip last line if it has no characters\n          if (\n            this.state.wasQuoting === true ||\n            this.state.record.length !== 0 ||\n            this.state.field.length !== 0\n          ) {\n            this.info.bytes = this.state.bufBytesStart + pos;\n            const errField = this.__onField();\n            if (errField !== undefined) return errField;\n            const errRecord = this.__onRecord(push);\n            if (errRecord !== undefined) return errRecord;\n          } else if (this.state.wasRowDelimiter === true) {\n            this.info.empty_lines++;\n          } else if (this.state.commenting === true) {\n            this.info.comment_lines++;\n          }\n        }\n      } else {\n        this.state.bufBytesStart += pos;\n        this.state.previousBuf = buf.slice(pos);\n      }\n      if (this.state.wasRowDelimiter === true) {\n        this.info.lines++;\n        this.state.wasRowDelimiter = false;\n      }\n    },\n    __onRecord: function (push) {\n      const {\n        columns,\n        group_columns_by_name,\n        encoding,\n        info,\n        from,\n        relax_column_count,\n        relax_column_count_less,\n        relax_column_count_more,\n        raw,\n        skip_records_with_empty_values,\n      } = this.options;\n      const { enabled, record } = this.state;\n      if (enabled === false) {\n        return this.__resetRecord();\n      }\n      // Convert the first line into column names\n      const recordLength = record.length;\n      if (columns === true) {\n        if (skip_records_with_empty_values === true && isRecordEmpty(record)) {\n          this.__resetRecord();\n          return;\n        }\n        return this.__firstLineToColumns(record);\n      }\n      if (columns === false && this.info.records === 0) {\n        this.state.expectedRecordLength = recordLength;\n      }\n      if (recordLength !== this.state.expectedRecordLength) {\n        const err =\n          columns === false\n            ? new _CsvError_js__WEBPACK_IMPORTED_MODULE_3__.CsvError(\n                \"CSV_RECORD_INCONSISTENT_FIELDS_LENGTH\",\n                [\n                  \"Invalid Record Length:\",\n                  `expect ${this.state.expectedRecordLength},`,\n                  `got ${recordLength} on line ${this.info.lines}`,\n                ],\n                this.options,\n                this.__infoField(),\n                {\n                  record: record,\n                },\n              )\n            : new _CsvError_js__WEBPACK_IMPORTED_MODULE_3__.CsvError(\n                \"CSV_RECORD_INCONSISTENT_COLUMNS\",\n                [\n                  \"Invalid Record Length:\",\n                  `columns length is ${columns.length},`, // rename columns\n                  `got ${recordLength} on line ${this.info.lines}`,\n                ],\n                this.options,\n                this.__infoField(),\n                {\n                  record: record,\n                },\n              );\n        if (\n          relax_column_count === true ||\n          (relax_column_count_less === true &&\n            recordLength < this.state.expectedRecordLength) ||\n          (relax_column_count_more === true &&\n            recordLength > this.state.expectedRecordLength)\n        ) {\n          this.info.invalid_field_length++;\n          this.state.error = err;\n          // Error is undefined with skip_records_with_error\n        } else {\n          const finalErr = this.__error(err);\n          if (finalErr) return finalErr;\n        }\n      }\n      if (skip_records_with_empty_values === true && isRecordEmpty(record)) {\n        this.__resetRecord();\n        return;\n      }\n      if (this.state.recordHasError === true) {\n        this.__resetRecord();\n        this.state.recordHasError = false;\n        return;\n      }\n      this.info.records++;\n      if (from === 1 || this.info.records >= from) {\n        const { objname } = this.options;\n        // With columns, records are object\n        if (columns !== false) {\n          const obj = {};\n          // Transform record array to an object\n          for (let i = 0, l = record.length; i < l; i++) {\n            if (columns[i] === undefined || columns[i].disabled) continue;\n            // Turn duplicate columns into an array\n            if (\n              group_columns_by_name === true &&\n              obj[columns[i].name] !== undefined\n            ) {\n              if (Array.isArray(obj[columns[i].name])) {\n                obj[columns[i].name] = obj[columns[i].name].concat(record[i]);\n              } else {\n                obj[columns[i].name] = [obj[columns[i].name], record[i]];\n              }\n            } else {\n              obj[columns[i].name] = record[i];\n            }\n          }\n          // Without objname (default)\n          if (raw === true || info === true) {\n            const extRecord = Object.assign(\n              { record: obj },\n              raw === true\n                ? { raw: this.state.rawBuffer.toString(encoding) }\n                : {},\n              info === true ? { info: this.__infoRecord() } : {},\n            );\n            const err = this.__push(\n              objname === undefined ? extRecord : [obj[objname], extRecord],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          } else {\n            const err = this.__push(\n              objname === undefined ? obj : [obj[objname], obj],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          }\n          // Without columns, records are array\n        } else {\n          if (raw === true || info === true) {\n            const extRecord = Object.assign(\n              { record: record },\n              raw === true\n                ? { raw: this.state.rawBuffer.toString(encoding) }\n                : {},\n              info === true ? { info: this.__infoRecord() } : {},\n            );\n            const err = this.__push(\n              objname === undefined ? extRecord : [record[objname], extRecord],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          } else {\n            const err = this.__push(\n              objname === undefined ? record : [record[objname], record],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          }\n        }\n      }\n      this.__resetRecord();\n    },\n    __firstLineToColumns: function (record) {\n      const { firstLineToHeaders } = this.state;\n      try {\n        const headers =\n          firstLineToHeaders === undefined\n            ? record\n            : firstLineToHeaders.call(null, record);\n        if (!Array.isArray(headers)) {\n          return this.__error(\n            new _CsvError_js__WEBPACK_IMPORTED_MODULE_3__.CsvError(\n              \"CSV_INVALID_COLUMN_MAPPING\",\n              [\n                \"Invalid Column Mapping:\",\n                \"expect an array from column function,\",\n                `got ${JSON.stringify(headers)}`,\n              ],\n              this.options,\n              this.__infoField(),\n              {\n                headers: headers,\n              },\n            ),\n          );\n        }\n        const normalizedHeaders = (0,_normalize_columns_array_js__WEBPACK_IMPORTED_MODULE_0__.normalize_columns_array)(headers);\n        this.state.expectedRecordLength = normalizedHeaders.length;\n        this.options.columns = normalizedHeaders;\n        this.__resetRecord();\n        return;\n      } catch (err) {\n        return err;\n      }\n    },\n    __resetRecord: function () {\n      if (this.options.raw === true) {\n        this.state.rawBuffer.reset();\n      }\n      this.state.error = undefined;\n      this.state.record = [];\n      this.state.record_length = 0;\n    },\n    __onField: function () {\n      const { cast, encoding, rtrim, max_record_size } = this.options;\n      const { enabled, wasQuoting } = this.state;\n      // Short circuit for the from_line options\n      if (enabled === false) {\n        return this.__resetField();\n      }\n      let field = this.state.field.toString(encoding);\n      if (rtrim === true && wasQuoting === false) {\n        field = field.trimRight();\n      }\n      if (cast === true) {\n        const [err, f] = this.__cast(field);\n        if (err !== undefined) return err;\n        field = f;\n      }\n      this.state.record.push(field);\n      // Increment record length if record size must not exceed a limit\n      if (max_record_size !== 0 && typeof field === \"string\") {\n        this.state.record_length += field.length;\n      }\n      this.__resetField();\n    },\n    __resetField: function () {\n      this.state.field.reset();\n      this.state.wasQuoting = false;\n    },\n    __push: function (record, push) {\n      const { on_record } = this.options;\n      if (on_record !== undefined) {\n        const info = this.__infoRecord();\n        try {\n          record = on_record.call(null, record, info);\n        } catch (err) {\n          return err;\n        }\n        if (record === undefined || record === null) {\n          return;\n        }\n      }\n      push(record);\n    },\n    // Return a tuple with the error and the casted value\n    __cast: function (field) {\n      const { columns, relax_column_count } = this.options;\n      const isColumns = Array.isArray(columns);\n      // Dont loose time calling cast\n      // because the final record is an object\n      // and this field can't be associated to a key present in columns\n      if (\n        isColumns === true &&\n        relax_column_count &&\n        this.options.columns.length <= this.state.record.length\n      ) {\n        return [undefined, undefined];\n      }\n      if (this.state.castField !== null) {\n        try {\n          const info = this.__infoField();\n          return [undefined, this.state.castField.call(null, field, info)];\n        } catch (err) {\n          return [err];\n        }\n      }\n      if (this.__isFloat(field)) {\n        return [undefined, parseFloat(field)];\n      } else if (this.options.cast_date !== false) {\n        const info = this.__infoField();\n        return [undefined, this.options.cast_date.call(null, field, info)];\n      }\n      return [undefined, field];\n    },\n    // Helper to test if a character is a space or a line delimiter\n    __isCharTrimable: function (buf, pos) {\n      const isTrim = (buf, pos) => {\n        const { timchars } = this.state;\n        loop1: for (let i = 0; i < timchars.length; i++) {\n          const timchar = timchars[i];\n          for (let j = 0; j < timchar.length; j++) {\n            if (timchar[j] !== buf[pos + j]) continue loop1;\n          }\n          return timchar.length;\n        }\n        return 0;\n      };\n      return isTrim(buf, pos);\n    },\n    // Keep it in case we implement the `cast_int` option\n    // __isInt(value){\n    //   // return Number.isInteger(parseInt(value))\n    //   // return !isNaN( parseInt( obj ) );\n    //   return /^(\\-|\\+)?[1-9][0-9]*$/.test(value)\n    // }\n    __isFloat: function (value) {\n      return value - parseFloat(value) + 1 >= 0; // Borrowed from jquery\n    },\n    __compareBytes: function (sourceBuf, targetBuf, targetPos, firstByte) {\n      if (sourceBuf[0] !== firstByte) return 0;\n      const sourceLength = sourceBuf.length;\n      for (let i = 1; i < sourceLength; i++) {\n        if (sourceBuf[i] !== targetBuf[targetPos + i]) return 0;\n      }\n      return sourceLength;\n    },\n    __isDelimiter: function (buf, pos, chr) {\n      const { delimiter, ignore_last_delimiters } = this.options;\n      if (\n        ignore_last_delimiters === true &&\n        this.state.record.length === this.options.columns.length - 1\n      ) {\n        return 0;\n      } else if (\n        ignore_last_delimiters !== false &&\n        typeof ignore_last_delimiters === \"number\" &&\n        this.state.record.length === ignore_last_delimiters - 1\n      ) {\n        return 0;\n      }\n      loop1: for (let i = 0; i < delimiter.length; i++) {\n        const del = delimiter[i];\n        if (del[0] === chr) {\n          for (let j = 1; j < del.length; j++) {\n            if (del[j] !== buf[pos + j]) continue loop1;\n          }\n          return del.length;\n        }\n      }\n      return 0;\n    },\n    __isRecordDelimiter: function (chr, buf, pos) {\n      const { record_delimiter } = this.options;\n      const recordDelimiterLength = record_delimiter.length;\n      loop1: for (let i = 0; i < recordDelimiterLength; i++) {\n        const rd = record_delimiter[i];\n        const rdLength = rd.length;\n        if (rd[0] !== chr) {\n          continue;\n        }\n        for (let j = 1; j < rdLength; j++) {\n          if (rd[j] !== buf[pos + j]) {\n            continue loop1;\n          }\n        }\n        return rd.length;\n      }\n      return 0;\n    },\n    __isEscape: function (buf, pos, chr) {\n      const { escape } = this.options;\n      if (escape === null) return false;\n      const l = escape.length;\n      if (escape[0] === chr) {\n        for (let i = 0; i < l; i++) {\n          if (escape[i] !== buf[pos + i]) {\n            return false;\n          }\n        }\n        return true;\n      }\n      return false;\n    },\n    __isQuote: function (buf, pos) {\n      const { quote } = this.options;\n      if (quote === null) return false;\n      const l = quote.length;\n      for (let i = 0; i < l; i++) {\n        if (quote[i] !== buf[pos + i]) {\n          return false;\n        }\n      }\n      return true;\n    },\n    __autoDiscoverRecordDelimiter: function (buf, pos) {\n      const { encoding } = this.options;\n      // Note, we don't need to cache this information in state,\n      // It is only called on the first line until we find out a suitable\n      // record delimiter.\n      const rds = [\n        // Important, the windows line ending must be before mac os 9\n        Buffer.from(\"\\r\\n\", encoding),\n        Buffer.from(\"\\n\", encoding),\n        Buffer.from(\"\\r\", encoding),\n      ];\n      loop: for (let i = 0; i < rds.length; i++) {\n        const l = rds[i].length;\n        for (let j = 0; j < l; j++) {\n          if (rds[i][j] !== buf[pos + j]) {\n            continue loop;\n          }\n        }\n        this.options.record_delimiter.push(rds[i]);\n        this.state.recordDelimiterMaxLength = rds[i].length;\n        return rds[i].length;\n      }\n      return 0;\n    },\n    __error: function (msg) {\n      const { encoding, raw, skip_records_with_error } = this.options;\n      const err = typeof msg === \"string\" ? new Error(msg) : msg;\n      if (skip_records_with_error) {\n        this.state.recordHasError = true;\n        if (this.options.on_skip !== undefined) {\n          this.options.on_skip(\n            err,\n            raw ? this.state.rawBuffer.toString(encoding) : undefined,\n          );\n        }\n        // this.emit('skip', err, raw ? this.state.rawBuffer.toString(encoding) : undefined);\n        return undefined;\n      } else {\n        return err;\n      }\n    },\n    __infoDataSet: function () {\n      return {\n        ...this.info,\n        columns: this.options.columns,\n      };\n    },\n    __infoRecord: function () {\n      const { columns, raw, encoding } = this.options;\n      return {\n        ...this.__infoDataSet(),\n        error: this.state.error,\n        header: columns === true,\n        index: this.state.record.length,\n        raw: raw ? this.state.rawBuffer.toString(encoding) : undefined,\n      };\n    },\n    __infoField: function () {\n      const { columns } = this.options;\n      const isColumns = Array.isArray(columns);\n      return {\n        ...this.__infoRecord(),\n        column:\n          isColumns === true\n            ? columns.length > this.state.record.length\n              ? columns[this.state.record.length].name\n              : null\n            : this.state.record.length,\n        quoting: this.state.wasQuoting,\n      };\n    },\n  };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/csv-parse/lib/api/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/csv-parse/lib/api/init_state.js":
/*!******************************************************!*\
  !*** ./node_modules/csv-parse/lib/api/init_state.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   init_state: () => (/* binding */ init_state)\n/* harmony export */ });\n/* harmony import */ var _utils_ResizeableBuffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils/ResizeableBuffer.js */ \"(rsc)/./node_modules/csv-parse/lib/utils/ResizeableBuffer.js\");\n\n\n// white space characters\n// https://en.wikipedia.org/wiki/Whitespace_character\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions/Character_Classes#Types\n// \\f\\n\\r\\t\\v\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff\nconst np = 12;\nconst cr = 13; // `\\r`, carriage return, 0x0D in hexadécimal, 13 in decimal\nconst nl = 10; // `\\n`, newline, 0x0A in hexadecimal, 10 in decimal\nconst space = 32;\nconst tab = 9;\n\nconst init_state = function (options) {\n  return {\n    bomSkipped: false,\n    bufBytesStart: 0,\n    castField: options.cast_function,\n    commenting: false,\n    // Current error encountered by a record\n    error: undefined,\n    enabled: options.from_line === 1,\n    escaping: false,\n    escapeIsQuote:\n      Buffer.isBuffer(options.escape) &&\n      Buffer.isBuffer(options.quote) &&\n      Buffer.compare(options.escape, options.quote) === 0,\n    // columns can be `false`, `true`, `Array`\n    expectedRecordLength: Array.isArray(options.columns)\n      ? options.columns.length\n      : undefined,\n    field: new _utils_ResizeableBuffer_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](20),\n    firstLineToHeaders: options.cast_first_line_to_header,\n    needMoreDataSize: Math.max(\n      // Skip if the remaining buffer smaller than comment\n      options.comment !== null ? options.comment.length : 0,\n      // Skip if the remaining buffer can be delimiter\n      ...options.delimiter.map((delimiter) => delimiter.length),\n      // Skip if the remaining buffer can be escape sequence\n      options.quote !== null ? options.quote.length : 0,\n    ),\n    previousBuf: undefined,\n    quoting: false,\n    stop: false,\n    rawBuffer: new _utils_ResizeableBuffer_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](100),\n    record: [],\n    recordHasError: false,\n    record_length: 0,\n    recordDelimiterMaxLength:\n      options.record_delimiter.length === 0\n        ? 0\n        : Math.max(...options.record_delimiter.map((v) => v.length)),\n    trimChars: [\n      Buffer.from(\" \", options.encoding)[0],\n      Buffer.from(\"\\t\", options.encoding)[0],\n    ],\n    wasQuoting: false,\n    wasRowDelimiter: false,\n    timchars: [\n      Buffer.from(Buffer.from([cr], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([nl], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([np], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([space], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([tab], \"utf8\").toString(), options.encoding),\n    ],\n  };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/csv-parse/lib/api/init_state.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/csv-parse/lib/api/normalize_columns_array.js":
/*!*******************************************************************!*\
  !*** ./node_modules/csv-parse/lib/api/normalize_columns_array.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalize_columns_array: () => (/* binding */ normalize_columns_array)\n/* harmony export */ });\n/* harmony import */ var _CsvError_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./CsvError.js */ \"(rsc)/./node_modules/csv-parse/lib/api/CsvError.js\");\n/* harmony import */ var _utils_is_object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/is_object.js */ \"(rsc)/./node_modules/csv-parse/lib/utils/is_object.js\");\n\n\n\nconst normalize_columns_array = function (columns) {\n  const normalizedColumns = [];\n  for (let i = 0, l = columns.length; i < l; i++) {\n    const column = columns[i];\n    if (column === undefined || column === null || column === false) {\n      normalizedColumns[i] = { disabled: true };\n    } else if (typeof column === \"string\") {\n      normalizedColumns[i] = { name: column };\n    } else if ((0,_utils_is_object_js__WEBPACK_IMPORTED_MODULE_1__.is_object)(column)) {\n      if (typeof column.name !== \"string\") {\n        throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_0__.CsvError(\"CSV_OPTION_COLUMNS_MISSING_NAME\", [\n          \"Option columns missing name:\",\n          `property \"name\" is required at position ${i}`,\n          \"when column is an object literal\",\n        ]);\n      }\n      normalizedColumns[i] = column;\n    } else {\n      throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_0__.CsvError(\"CSV_INVALID_COLUMN_DEFINITION\", [\n        \"Invalid column definition:\",\n        \"expect a string or a literal object,\",\n        `got ${JSON.stringify(column)} at position ${i}`,\n      ]);\n    }\n  }\n  return normalizedColumns;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/csv-parse/lib/api/normalize_columns_array.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/csv-parse/lib/api/normalize_options.js":
/*!*************************************************************!*\
  !*** ./node_modules/csv-parse/lib/api/normalize_options.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   normalize_options: () => (/* binding */ normalize_options)\n/* harmony export */ });\n/* harmony import */ var _normalize_columns_array_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./normalize_columns_array.js */ \"(rsc)/./node_modules/csv-parse/lib/api/normalize_columns_array.js\");\n/* harmony import */ var _CsvError_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CsvError.js */ \"(rsc)/./node_modules/csv-parse/lib/api/CsvError.js\");\n/* harmony import */ var _utils_underscore_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/underscore.js */ \"(rsc)/./node_modules/csv-parse/lib/utils/underscore.js\");\n\n\n\n\nconst normalize_options = function (opts) {\n  const options = {};\n  // Merge with user options\n  for (const opt in opts) {\n    options[(0,_utils_underscore_js__WEBPACK_IMPORTED_MODULE_2__.underscore)(opt)] = opts[opt];\n  }\n  // Normalize option `encoding`\n  // Note: defined first because other options depends on it\n  // to convert chars/strings into buffers.\n  if (options.encoding === undefined || options.encoding === true) {\n    options.encoding = \"utf8\";\n  } else if (options.encoding === null || options.encoding === false) {\n    options.encoding = null;\n  } else if (\n    typeof options.encoding !== \"string\" &&\n    options.encoding !== null\n  ) {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_ENCODING\",\n      [\n        \"Invalid option encoding:\",\n        \"encoding must be a string or null to return a buffer,\",\n        `got ${JSON.stringify(options.encoding)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `bom`\n  if (\n    options.bom === undefined ||\n    options.bom === null ||\n    options.bom === false\n  ) {\n    options.bom = false;\n  } else if (options.bom !== true) {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_BOM\",\n      [\n        \"Invalid option bom:\",\n        \"bom must be true,\",\n        `got ${JSON.stringify(options.bom)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `cast`\n  options.cast_function = null;\n  if (\n    options.cast === undefined ||\n    options.cast === null ||\n    options.cast === false ||\n    options.cast === \"\"\n  ) {\n    options.cast = undefined;\n  } else if (typeof options.cast === \"function\") {\n    options.cast_function = options.cast;\n    options.cast = true;\n  } else if (options.cast !== true) {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_CAST\",\n      [\n        \"Invalid option cast:\",\n        \"cast must be true or a function,\",\n        `got ${JSON.stringify(options.cast)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `cast_date`\n  if (\n    options.cast_date === undefined ||\n    options.cast_date === null ||\n    options.cast_date === false ||\n    options.cast_date === \"\"\n  ) {\n    options.cast_date = false;\n  } else if (options.cast_date === true) {\n    options.cast_date = function (value) {\n      const date = Date.parse(value);\n      return !isNaN(date) ? new Date(date) : value;\n    };\n  } else if (typeof options.cast_date !== \"function\") {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_CAST_DATE\",\n      [\n        \"Invalid option cast_date:\",\n        \"cast_date must be true or a function,\",\n        `got ${JSON.stringify(options.cast_date)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `columns`\n  options.cast_first_line_to_header = null;\n  if (options.columns === true) {\n    // Fields in the first line are converted as-is to columns\n    options.cast_first_line_to_header = undefined;\n  } else if (typeof options.columns === \"function\") {\n    options.cast_first_line_to_header = options.columns;\n    options.columns = true;\n  } else if (Array.isArray(options.columns)) {\n    options.columns = (0,_normalize_columns_array_js__WEBPACK_IMPORTED_MODULE_0__.normalize_columns_array)(options.columns);\n  } else if (\n    options.columns === undefined ||\n    options.columns === null ||\n    options.columns === false\n  ) {\n    options.columns = false;\n  } else {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_COLUMNS\",\n      [\n        \"Invalid option columns:\",\n        \"expect an array, a function or true,\",\n        `got ${JSON.stringify(options.columns)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `group_columns_by_name`\n  if (\n    options.group_columns_by_name === undefined ||\n    options.group_columns_by_name === null ||\n    options.group_columns_by_name === false\n  ) {\n    options.group_columns_by_name = false;\n  } else if (options.group_columns_by_name !== true) {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_GROUP_COLUMNS_BY_NAME\",\n      [\n        \"Invalid option group_columns_by_name:\",\n        \"expect an boolean,\",\n        `got ${JSON.stringify(options.group_columns_by_name)}`,\n      ],\n      options,\n    );\n  } else if (options.columns === false) {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_GROUP_COLUMNS_BY_NAME\",\n      [\n        \"Invalid option group_columns_by_name:\",\n        \"the `columns` mode must be activated.\",\n      ],\n      options,\n    );\n  }\n  // Normalize option `comment`\n  if (\n    options.comment === undefined ||\n    options.comment === null ||\n    options.comment === false ||\n    options.comment === \"\"\n  ) {\n    options.comment = null;\n  } else {\n    if (typeof options.comment === \"string\") {\n      options.comment = Buffer.from(options.comment, options.encoding);\n    }\n    if (!Buffer.isBuffer(options.comment)) {\n      throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n        \"CSV_INVALID_OPTION_COMMENT\",\n        [\n          \"Invalid option comment:\",\n          \"comment must be a buffer or a string,\",\n          `got ${JSON.stringify(options.comment)}`,\n        ],\n        options,\n      );\n    }\n  }\n  // Normalize option `comment_no_infix`\n  if (\n    options.comment_no_infix === undefined ||\n    options.comment_no_infix === null ||\n    options.comment_no_infix === false\n  ) {\n    options.comment_no_infix = false;\n  } else if (options.comment_no_infix !== true) {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_COMMENT\",\n      [\n        \"Invalid option comment_no_infix:\",\n        \"value must be a boolean,\",\n        `got ${JSON.stringify(options.comment_no_infix)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `delimiter`\n  const delimiter_json = JSON.stringify(options.delimiter);\n  if (!Array.isArray(options.delimiter))\n    options.delimiter = [options.delimiter];\n  if (options.delimiter.length === 0) {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_DELIMITER\",\n      [\n        \"Invalid option delimiter:\",\n        \"delimiter must be a non empty string or buffer or array of string|buffer,\",\n        `got ${delimiter_json}`,\n      ],\n      options,\n    );\n  }\n  options.delimiter = options.delimiter.map(function (delimiter) {\n    if (delimiter === undefined || delimiter === null || delimiter === false) {\n      return Buffer.from(\",\", options.encoding);\n    }\n    if (typeof delimiter === \"string\") {\n      delimiter = Buffer.from(delimiter, options.encoding);\n    }\n    if (!Buffer.isBuffer(delimiter) || delimiter.length === 0) {\n      throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n        \"CSV_INVALID_OPTION_DELIMITER\",\n        [\n          \"Invalid option delimiter:\",\n          \"delimiter must be a non empty string or buffer or array of string|buffer,\",\n          `got ${delimiter_json}`,\n        ],\n        options,\n      );\n    }\n    return delimiter;\n  });\n  // Normalize option `escape`\n  if (options.escape === undefined || options.escape === true) {\n    options.escape = Buffer.from('\"', options.encoding);\n  } else if (typeof options.escape === \"string\") {\n    options.escape = Buffer.from(options.escape, options.encoding);\n  } else if (options.escape === null || options.escape === false) {\n    options.escape = null;\n  }\n  if (options.escape !== null) {\n    if (!Buffer.isBuffer(options.escape)) {\n      throw new Error(\n        `Invalid Option: escape must be a buffer, a string or a boolean, got ${JSON.stringify(options.escape)}`,\n      );\n    }\n  }\n  // Normalize option `from`\n  if (options.from === undefined || options.from === null) {\n    options.from = 1;\n  } else {\n    if (typeof options.from === \"string\" && /\\d+/.test(options.from)) {\n      options.from = parseInt(options.from);\n    }\n    if (Number.isInteger(options.from)) {\n      if (options.from < 0) {\n        throw new Error(\n          `Invalid Option: from must be a positive integer, got ${JSON.stringify(opts.from)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: from must be an integer, got ${JSON.stringify(options.from)}`,\n      );\n    }\n  }\n  // Normalize option `from_line`\n  if (options.from_line === undefined || options.from_line === null) {\n    options.from_line = 1;\n  } else {\n    if (\n      typeof options.from_line === \"string\" &&\n      /\\d+/.test(options.from_line)\n    ) {\n      options.from_line = parseInt(options.from_line);\n    }\n    if (Number.isInteger(options.from_line)) {\n      if (options.from_line <= 0) {\n        throw new Error(\n          `Invalid Option: from_line must be a positive integer greater than 0, got ${JSON.stringify(opts.from_line)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: from_line must be an integer, got ${JSON.stringify(opts.from_line)}`,\n      );\n    }\n  }\n  // Normalize options `ignore_last_delimiters`\n  if (\n    options.ignore_last_delimiters === undefined ||\n    options.ignore_last_delimiters === null\n  ) {\n    options.ignore_last_delimiters = false;\n  } else if (typeof options.ignore_last_delimiters === \"number\") {\n    options.ignore_last_delimiters = Math.floor(options.ignore_last_delimiters);\n    if (options.ignore_last_delimiters === 0) {\n      options.ignore_last_delimiters = false;\n    }\n  } else if (typeof options.ignore_last_delimiters !== \"boolean\") {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_IGNORE_LAST_DELIMITERS\",\n      [\n        \"Invalid option `ignore_last_delimiters`:\",\n        \"the value must be a boolean value or an integer,\",\n        `got ${JSON.stringify(options.ignore_last_delimiters)}`,\n      ],\n      options,\n    );\n  }\n  if (options.ignore_last_delimiters === true && options.columns === false) {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_IGNORE_LAST_DELIMITERS_REQUIRES_COLUMNS\",\n      [\n        \"The option `ignore_last_delimiters`\",\n        \"requires the activation of the `columns` option\",\n      ],\n      options,\n    );\n  }\n  // Normalize option `info`\n  if (\n    options.info === undefined ||\n    options.info === null ||\n    options.info === false\n  ) {\n    options.info = false;\n  } else if (options.info !== true) {\n    throw new Error(\n      `Invalid Option: info must be true, got ${JSON.stringify(options.info)}`,\n    );\n  }\n  // Normalize option `max_record_size`\n  if (\n    options.max_record_size === undefined ||\n    options.max_record_size === null ||\n    options.max_record_size === false\n  ) {\n    options.max_record_size = 0;\n  } else if (\n    Number.isInteger(options.max_record_size) &&\n    options.max_record_size >= 0\n  ) {\n    // Great, nothing to do\n  } else if (\n    typeof options.max_record_size === \"string\" &&\n    /\\d+/.test(options.max_record_size)\n  ) {\n    options.max_record_size = parseInt(options.max_record_size);\n  } else {\n    throw new Error(\n      `Invalid Option: max_record_size must be a positive integer, got ${JSON.stringify(options.max_record_size)}`,\n    );\n  }\n  // Normalize option `objname`\n  if (\n    options.objname === undefined ||\n    options.objname === null ||\n    options.objname === false\n  ) {\n    options.objname = undefined;\n  } else if (Buffer.isBuffer(options.objname)) {\n    if (options.objname.length === 0) {\n      throw new Error(`Invalid Option: objname must be a non empty buffer`);\n    }\n    if (options.encoding === null) {\n      // Don't call `toString`, leave objname as a buffer\n    } else {\n      options.objname = options.objname.toString(options.encoding);\n    }\n  } else if (typeof options.objname === \"string\") {\n    if (options.objname.length === 0) {\n      throw new Error(`Invalid Option: objname must be a non empty string`);\n    }\n    // Great, nothing to do\n  } else if (typeof options.objname === \"number\") {\n    // if(options.objname.length === 0){\n    //   throw new Error(`Invalid Option: objname must be a non empty string`);\n    // }\n    // Great, nothing to do\n  } else {\n    throw new Error(\n      `Invalid Option: objname must be a string or a buffer, got ${options.objname}`,\n    );\n  }\n  if (options.objname !== undefined) {\n    if (typeof options.objname === \"number\") {\n      if (options.columns !== false) {\n        throw Error(\n          \"Invalid Option: objname index cannot be combined with columns or be defined as a field\",\n        );\n      }\n    } else {\n      // A string or a buffer\n      if (options.columns === false) {\n        throw Error(\n          \"Invalid Option: objname field must be combined with columns or be defined as an index\",\n        );\n      }\n    }\n  }\n  // Normalize option `on_record`\n  if (options.on_record === undefined || options.on_record === null) {\n    options.on_record = undefined;\n  } else if (typeof options.on_record !== \"function\") {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_ON_RECORD\",\n      [\n        \"Invalid option `on_record`:\",\n        \"expect a function,\",\n        `got ${JSON.stringify(options.on_record)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `on_skip`\n  // options.on_skip ??= (err, chunk) => {\n  //   this.emit('skip', err, chunk);\n  // };\n  if (\n    options.on_skip !== undefined &&\n    options.on_skip !== null &&\n    typeof options.on_skip !== \"function\"\n  ) {\n    throw new Error(\n      `Invalid Option: on_skip must be a function, got ${JSON.stringify(options.on_skip)}`,\n    );\n  }\n  // Normalize option `quote`\n  if (\n    options.quote === null ||\n    options.quote === false ||\n    options.quote === \"\"\n  ) {\n    options.quote = null;\n  } else {\n    if (options.quote === undefined || options.quote === true) {\n      options.quote = Buffer.from('\"', options.encoding);\n    } else if (typeof options.quote === \"string\") {\n      options.quote = Buffer.from(options.quote, options.encoding);\n    }\n    if (!Buffer.isBuffer(options.quote)) {\n      throw new Error(\n        `Invalid Option: quote must be a buffer or a string, got ${JSON.stringify(options.quote)}`,\n      );\n    }\n  }\n  // Normalize option `raw`\n  if (\n    options.raw === undefined ||\n    options.raw === null ||\n    options.raw === false\n  ) {\n    options.raw = false;\n  } else if (options.raw !== true) {\n    throw new Error(\n      `Invalid Option: raw must be true, got ${JSON.stringify(options.raw)}`,\n    );\n  }\n  // Normalize option `record_delimiter`\n  if (options.record_delimiter === undefined) {\n    options.record_delimiter = [];\n  } else if (\n    typeof options.record_delimiter === \"string\" ||\n    Buffer.isBuffer(options.record_delimiter)\n  ) {\n    if (options.record_delimiter.length === 0) {\n      throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n        \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n        [\n          \"Invalid option `record_delimiter`:\",\n          \"value must be a non empty string or buffer,\",\n          `got ${JSON.stringify(options.record_delimiter)}`,\n        ],\n        options,\n      );\n    }\n    options.record_delimiter = [options.record_delimiter];\n  } else if (!Array.isArray(options.record_delimiter)) {\n    throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n      \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n      [\n        \"Invalid option `record_delimiter`:\",\n        \"value must be a string, a buffer or array of string|buffer,\",\n        `got ${JSON.stringify(options.record_delimiter)}`,\n      ],\n      options,\n    );\n  }\n  options.record_delimiter = options.record_delimiter.map(function (rd, i) {\n    if (typeof rd !== \"string\" && !Buffer.isBuffer(rd)) {\n      throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n        \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n        [\n          \"Invalid option `record_delimiter`:\",\n          \"value must be a string, a buffer or array of string|buffer\",\n          `at index ${i},`,\n          `got ${JSON.stringify(rd)}`,\n        ],\n        options,\n      );\n    } else if (rd.length === 0) {\n      throw new _CsvError_js__WEBPACK_IMPORTED_MODULE_1__.CsvError(\n        \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n        [\n          \"Invalid option `record_delimiter`:\",\n          \"value must be a non empty string or buffer\",\n          `at index ${i},`,\n          `got ${JSON.stringify(rd)}`,\n        ],\n        options,\n      );\n    }\n    if (typeof rd === \"string\") {\n      rd = Buffer.from(rd, options.encoding);\n    }\n    return rd;\n  });\n  // Normalize option `relax_column_count`\n  if (typeof options.relax_column_count === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.relax_column_count === undefined ||\n    options.relax_column_count === null\n  ) {\n    options.relax_column_count = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_column_count must be a boolean, got ${JSON.stringify(options.relax_column_count)}`,\n    );\n  }\n  if (typeof options.relax_column_count_less === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.relax_column_count_less === undefined ||\n    options.relax_column_count_less === null\n  ) {\n    options.relax_column_count_less = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_column_count_less must be a boolean, got ${JSON.stringify(options.relax_column_count_less)}`,\n    );\n  }\n  if (typeof options.relax_column_count_more === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.relax_column_count_more === undefined ||\n    options.relax_column_count_more === null\n  ) {\n    options.relax_column_count_more = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_column_count_more must be a boolean, got ${JSON.stringify(options.relax_column_count_more)}`,\n    );\n  }\n  // Normalize option `relax_quotes`\n  if (typeof options.relax_quotes === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.relax_quotes === undefined ||\n    options.relax_quotes === null\n  ) {\n    options.relax_quotes = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_quotes must be a boolean, got ${JSON.stringify(options.relax_quotes)}`,\n    );\n  }\n  // Normalize option `skip_empty_lines`\n  if (typeof options.skip_empty_lines === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.skip_empty_lines === undefined ||\n    options.skip_empty_lines === null\n  ) {\n    options.skip_empty_lines = false;\n  } else {\n    throw new Error(\n      `Invalid Option: skip_empty_lines must be a boolean, got ${JSON.stringify(options.skip_empty_lines)}`,\n    );\n  }\n  // Normalize option `skip_records_with_empty_values`\n  if (typeof options.skip_records_with_empty_values === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.skip_records_with_empty_values === undefined ||\n    options.skip_records_with_empty_values === null\n  ) {\n    options.skip_records_with_empty_values = false;\n  } else {\n    throw new Error(\n      `Invalid Option: skip_records_with_empty_values must be a boolean, got ${JSON.stringify(options.skip_records_with_empty_values)}`,\n    );\n  }\n  // Normalize option `skip_records_with_error`\n  if (typeof options.skip_records_with_error === \"boolean\") {\n    // Great, nothing to do\n  } else if (\n    options.skip_records_with_error === undefined ||\n    options.skip_records_with_error === null\n  ) {\n    options.skip_records_with_error = false;\n  } else {\n    throw new Error(\n      `Invalid Option: skip_records_with_error must be a boolean, got ${JSON.stringify(options.skip_records_with_error)}`,\n    );\n  }\n  // Normalize option `rtrim`\n  if (\n    options.rtrim === undefined ||\n    options.rtrim === null ||\n    options.rtrim === false\n  ) {\n    options.rtrim = false;\n  } else if (options.rtrim !== true) {\n    throw new Error(\n      `Invalid Option: rtrim must be a boolean, got ${JSON.stringify(options.rtrim)}`,\n    );\n  }\n  // Normalize option `ltrim`\n  if (\n    options.ltrim === undefined ||\n    options.ltrim === null ||\n    options.ltrim === false\n  ) {\n    options.ltrim = false;\n  } else if (options.ltrim !== true) {\n    throw new Error(\n      `Invalid Option: ltrim must be a boolean, got ${JSON.stringify(options.ltrim)}`,\n    );\n  }\n  // Normalize option `trim`\n  if (\n    options.trim === undefined ||\n    options.trim === null ||\n    options.trim === false\n  ) {\n    options.trim = false;\n  } else if (options.trim !== true) {\n    throw new Error(\n      `Invalid Option: trim must be a boolean, got ${JSON.stringify(options.trim)}`,\n    );\n  }\n  // Normalize options `trim`, `ltrim` and `rtrim`\n  if (options.trim === true && opts.ltrim !== false) {\n    options.ltrim = true;\n  } else if (options.ltrim !== true) {\n    options.ltrim = false;\n  }\n  if (options.trim === true && opts.rtrim !== false) {\n    options.rtrim = true;\n  } else if (options.rtrim !== true) {\n    options.rtrim = false;\n  }\n  // Normalize option `to`\n  if (options.to === undefined || options.to === null) {\n    options.to = -1;\n  } else {\n    if (typeof options.to === \"string\" && /\\d+/.test(options.to)) {\n      options.to = parseInt(options.to);\n    }\n    if (Number.isInteger(options.to)) {\n      if (options.to <= 0) {\n        throw new Error(\n          `Invalid Option: to must be a positive integer greater than 0, got ${JSON.stringify(opts.to)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: to must be an integer, got ${JSON.stringify(opts.to)}`,\n      );\n    }\n  }\n  // Normalize option `to_line`\n  if (options.to_line === undefined || options.to_line === null) {\n    options.to_line = -1;\n  } else {\n    if (typeof options.to_line === \"string\" && /\\d+/.test(options.to_line)) {\n      options.to_line = parseInt(options.to_line);\n    }\n    if (Number.isInteger(options.to_line)) {\n      if (options.to_line <= 0) {\n        throw new Error(\n          `Invalid Option: to_line must be a positive integer greater than 0, got ${JSON.stringify(opts.to_line)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: to_line must be an integer, got ${JSON.stringify(opts.to_line)}`,\n      );\n    }\n  }\n  return options;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/csv-parse/lib/api/normalize_options.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/csv-parse/lib/sync.js":
/*!********************************************!*\
  !*** ./node_modules/csv-parse/lib/sync.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CsvError: () => (/* reexport safe */ _api_index_js__WEBPACK_IMPORTED_MODULE_0__.CsvError),\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var _api_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api/index.js */ \"(rsc)/./node_modules/csv-parse/lib/api/index.js\");\n\n\nconst parse = function (data, opts = {}) {\n  if (typeof data === \"string\") {\n    data = Buffer.from(data);\n  }\n  const records = opts && opts.objname ? {} : [];\n  const parser = (0,_api_index_js__WEBPACK_IMPORTED_MODULE_0__.transform)(opts);\n  const push = (record) => {\n    if (parser.options.objname === undefined) records.push(record);\n    else {\n      records[record[0]] = record[1];\n    }\n  };\n  const close = () => {};\n  const err1 = parser.parse(data, false, push, close);\n  if (err1 !== undefined) throw err1;\n  const err2 = parser.parse(undefined, true, push, close);\n  if (err2 !== undefined) throw err2;\n  return records;\n};\n\n// export default parse\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY3N2LXBhcnNlL2xpYi9zeW5jLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFxRDs7QUFFckQsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QztBQUM1QyxpQkFBaUIsd0RBQVM7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDaUI7QUFDRyIsInNvdXJjZXMiOlsiRDpcXGNvZGVcXG9wZW5haS10ZXN0aW5nLWFnZW50LWRlbW9cXHNhbXBsZS10ZXN0LWFwcFxcbm9kZV9tb2R1bGVzXFxjc3YtcGFyc2VcXGxpYlxcc3luYy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDc3ZFcnJvciwgdHJhbnNmb3JtIH0gZnJvbSBcIi4vYXBpL2luZGV4LmpzXCI7XG5cbmNvbnN0IHBhcnNlID0gZnVuY3Rpb24gKGRhdGEsIG9wdHMgPSB7fSkge1xuICBpZiAodHlwZW9mIGRhdGEgPT09IFwic3RyaW5nXCIpIHtcbiAgICBkYXRhID0gQnVmZmVyLmZyb20oZGF0YSk7XG4gIH1cbiAgY29uc3QgcmVjb3JkcyA9IG9wdHMgJiYgb3B0cy5vYmpuYW1lID8ge30gOiBbXTtcbiAgY29uc3QgcGFyc2VyID0gdHJhbnNmb3JtKG9wdHMpO1xuICBjb25zdCBwdXNoID0gKHJlY29yZCkgPT4ge1xuICAgIGlmIChwYXJzZXIub3B0aW9ucy5vYmpuYW1lID09PSB1bmRlZmluZWQpIHJlY29yZHMucHVzaChyZWNvcmQpO1xuICAgIGVsc2Uge1xuICAgICAgcmVjb3Jkc1tyZWNvcmRbMF1dID0gcmVjb3JkWzFdO1xuICAgIH1cbiAgfTtcbiAgY29uc3QgY2xvc2UgPSAoKSA9PiB7fTtcbiAgY29uc3QgZXJyMSA9IHBhcnNlci5wYXJzZShkYXRhLCBmYWxzZSwgcHVzaCwgY2xvc2UpO1xuICBpZiAoZXJyMSAhPT0gdW5kZWZpbmVkKSB0aHJvdyBlcnIxO1xuICBjb25zdCBlcnIyID0gcGFyc2VyLnBhcnNlKHVuZGVmaW5lZCwgdHJ1ZSwgcHVzaCwgY2xvc2UpO1xuICBpZiAoZXJyMiAhPT0gdW5kZWZpbmVkKSB0aHJvdyBlcnIyO1xuICByZXR1cm4gcmVjb3Jkcztcbn07XG5cbi8vIGV4cG9ydCBkZWZhdWx0IHBhcnNlXG5leHBvcnQgeyBwYXJzZSB9O1xuZXhwb3J0IHsgQ3N2RXJyb3IgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/csv-parse/lib/sync.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/csv-parse/lib/utils/ResizeableBuffer.js":
/*!**************************************************************!*\
  !*** ./node_modules/csv-parse/lib/utils/ResizeableBuffer.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nclass ResizeableBuffer {\n  constructor(size = 100) {\n    this.size = size;\n    this.length = 0;\n    this.buf = Buffer.allocUnsafe(size);\n  }\n  prepend(val) {\n    if (Buffer.isBuffer(val)) {\n      const length = this.length + val.length;\n      if (length >= this.size) {\n        this.resize();\n        if (length >= this.size) {\n          throw Error(\"INVALID_BUFFER_STATE\");\n        }\n      }\n      const buf = this.buf;\n      this.buf = Buffer.allocUnsafe(this.size);\n      val.copy(this.buf, 0);\n      buf.copy(this.buf, val.length);\n      this.length += val.length;\n    } else {\n      const length = this.length++;\n      if (length === this.size) {\n        this.resize();\n      }\n      const buf = this.clone();\n      this.buf[0] = val;\n      buf.copy(this.buf, 1, 0, length);\n    }\n  }\n  append(val) {\n    const length = this.length++;\n    if (length === this.size) {\n      this.resize();\n    }\n    this.buf[length] = val;\n  }\n  clone() {\n    return Buffer.from(this.buf.slice(0, this.length));\n  }\n  resize() {\n    const length = this.length;\n    this.size = this.size * 2;\n    const buf = Buffer.allocUnsafe(this.size);\n    this.buf.copy(buf, 0, 0, length);\n    this.buf = buf;\n  }\n  toString(encoding) {\n    if (encoding) {\n      return this.buf.slice(0, this.length).toString(encoding);\n    } else {\n      return Uint8Array.prototype.slice.call(this.buf.slice(0, this.length));\n    }\n  }\n  toJSON() {\n    return this.toString(\"utf8\");\n  }\n  reset() {\n    this.length = 0;\n  }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ResizeableBuffer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/csv-parse/lib/utils/ResizeableBuffer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/csv-parse/lib/utils/is_object.js":
/*!*******************************************************!*\
  !*** ./node_modules/csv-parse/lib/utils/is_object.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   is_object: () => (/* binding */ is_object)\n/* harmony export */ });\nconst is_object = function (obj) {\n  return typeof obj === \"object\" && obj !== null && !Array.isArray(obj);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY3N2LXBhcnNlL2xpYi91dGlscy9pc19vYmplY3QuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTs7QUFFcUIiLCJzb3VyY2VzIjpbIkQ6XFxjb2RlXFxvcGVuYWktdGVzdGluZy1hZ2VudC1kZW1vXFxzYW1wbGUtdGVzdC1hcHBcXG5vZGVfbW9kdWxlc1xcY3N2LXBhcnNlXFxsaWJcXHV0aWxzXFxpc19vYmplY3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgaXNfb2JqZWN0ID0gZnVuY3Rpb24gKG9iaikge1xuICByZXR1cm4gdHlwZW9mIG9iaiA9PT0gXCJvYmplY3RcIiAmJiBvYmogIT09IG51bGwgJiYgIUFycmF5LmlzQXJyYXkob2JqKTtcbn07XG5cbmV4cG9ydCB7IGlzX29iamVjdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/csv-parse/lib/utils/is_object.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/csv-parse/lib/utils/underscore.js":
/*!********************************************************!*\
  !*** ./node_modules/csv-parse/lib/utils/underscore.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   underscore: () => (/* binding */ underscore)\n/* harmony export */ });\nconst underscore = function (str) {\n  return str.replace(/([A-Z])/g, function (_, match) {\n    return \"_\" + match.toLowerCase();\n  });\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY3N2LXBhcnNlL2xpYi91dGlscy91bmRlcnNjb3JlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRXNCIiwic291cmNlcyI6WyJEOlxcY29kZVxcb3BlbmFpLXRlc3RpbmctYWdlbnQtZGVtb1xcc2FtcGxlLXRlc3QtYXBwXFxub2RlX21vZHVsZXNcXGNzdi1wYXJzZVxcbGliXFx1dGlsc1xcdW5kZXJzY29yZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCB1bmRlcnNjb3JlID0gZnVuY3Rpb24gKHN0cikge1xuICByZXR1cm4gc3RyLnJlcGxhY2UoLyhbQS1aXSkvZywgZnVuY3Rpb24gKF8sIG1hdGNoKSB7XG4gICAgcmV0dXJuIFwiX1wiICsgbWF0Y2gudG9Mb3dlckNhc2UoKTtcbiAgfSk7XG59O1xuXG5leHBvcnQgeyB1bmRlcnNjb3JlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/csv-parse/lib/utils/underscore.js\n");

/***/ })

};
;