# 🚀 OpenRouter 集成指南

OpenRouter 是一个统一的 AI 模型 API 平台，让您可以通过单一接口访问多个 AI 提供商的模型，包括 Anthropic Claude、OpenAI GPT、Google Gemini、Meta Llama 等。

## 🌟 为什么选择 OpenRouter？

### 优势
- **多模型访问**: 一个 API 密钥访问所有主流 AI 模型
- **成本优化**: 透明的按使用量计费，通常比直接使用更便宜
- **高可用性**: 自动故障转移和负载均衡
- **统一接口**: 兼容 OpenAI API 格式，易于集成
- **实时定价**: 查看每个模型的实时价格和可用性

### 支持的模型
- **Anthropic**: Claude 3.5 Sonnet, Claude 3 Haiku
- **OpenAI**: GPT-4o, GPT-4o Mini, GPT-4 Turbo
- **Google**: Gemini Pro, Gemini Flash
- **Meta**: Llama 3.1 70B, Llama 3.1 8B
- **Alibaba**: Qwen 2.5 72B
- **更多**: 50+ 模型可选

## 🔧 设置步骤

### 1. 获取 OpenRouter API 密钥

1. 访问 [OpenRouter](https://openrouter.ai)
2. 注册账户并登录
3. 前往 [API Keys](https://openrouter.ai/keys) 页面
4. 创建新的 API 密钥
5. 复制密钥（格式：`sk-or-v1-...`）

### 2. 配置环境变量

在 `cua-server/.env.development` 文件中添加：

```bash
# OpenRouter API Key
OPENROUTER_API_KEY=sk-or-v1-your-actual-api-key-here

# 推荐模型 (可选)
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet

# 站点信息 (可选，用于使用统计)
OPENROUTER_SITE_URL=http://localhost:3000
OPENROUTER_SITE_NAME=AI Testing Agent
```

### 3. 测试设置

运行测试脚本验证配置：

```bash
node test-ai-models-setup.js
```

## 🎯 推荐模型

| 模型 | 用途 | 性价比 | 速度 | 质量 |
|------|------|--------|------|------|
| `anthropic/claude-3.5-sonnet` | 复杂测试任务 | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| `anthropic/claude-3-haiku` | 简单快速测试 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| `openai/gpt-4o-mini` | 平衡选择 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| `google/gemini-pro` | Google 生态 | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |

## 🚀 使用方法

### 1. 前端选择

1. 启动应用：`npm run dev`
2. 访问 http://localhost:3000
3. 在配置面板中选择 "OpenRouter (Recommended)"
4. 选择您偏好的模型
5. 开始测试

### 2. 环境变量控制

```bash
# 设置默认提供商为 OpenRouter
MODEL_PROVIDER=openrouter

# 设置默认模型
OPENROUTER_MODEL=anthropic/claude-3.5-sonnet
```

## 💰 成本优化

### 价格对比 (每 1M tokens)
- **Claude 3.5 Sonnet**: ~$3.00 (输入) / $15.00 (输出)
- **Claude 3 Haiku**: ~$0.25 (输入) / $1.25 (输出)
- **GPT-4o Mini**: ~$0.15 (输入) / $0.60 (输出)
- **Gemini Pro**: ~$0.50 (输入) / $1.50 (输出)

### 节省成本的技巧
1. **选择合适的模型**: 简单任务使用 Haiku 或 GPT-4o Mini
2. **监控使用量**: 在 OpenRouter 仪表板查看消费
3. **设置预算限制**: 避免意外超支
4. **批量处理**: 减少 API 调用次数

## 🔍 故障排除

### 常见问题

**Q: API 密钥无效**
```
Error: Invalid API key
```
**A**: 确保 API 密钥格式正确 (`sk-or-v1-...`) 并且有效

**Q: 模型不可用**
```
Error: Model not found
```
**A**: 检查模型名称是否正确，或在 [OpenRouter Models](https://openrouter.ai/models) 查看可用模型

**Q: 余额不足**
```
Error: Insufficient credits
```
**A**: 在 OpenRouter 账户中充值

**Q: 请求被限制**
```
Error: Rate limit exceeded
```
**A**: 等待一段时间或升级到更高的限制计划

### 调试步骤

1. **检查 API 密钥**:
   ```bash
   echo $OPENROUTER_API_KEY
   ```

2. **测试连接**:
   ```bash
   curl -X POST "https://openrouter.ai/api/v1/chat/completions" \
     -H "Authorization: Bearer $OPENROUTER_API_KEY" \
     -H "Content-Type: application/json" \
     -d '{"model": "anthropic/claude-3-haiku", "messages": [{"role": "user", "content": "Hello"}]}'
   ```

3. **查看日志**:
   ```bash
   # 在 cua-server 目录中
   npm run dev
   # 查看控制台输出
   ```

## 🔄 从其他提供商迁移

### 从 OpenAI 迁移
- 代码几乎无需更改（兼容 OpenAI API）
- 只需更换 API 密钥和 base URL
- 可以使用相同的 GPT 模型

### 从 Gemini 迁移
- 通过 OpenRouter 仍可使用 Gemini 模型
- 统一的 API 格式，更易管理
- 可以轻松切换到其他模型

## 📚 更多资源

- [OpenRouter 官网](https://openrouter.ai)
- [API 文档](https://openrouter.ai/docs)
- [模型列表](https://openrouter.ai/models)
- [定价信息](https://openrouter.ai/models#pricing)
- [使用统计](https://openrouter.ai/activity)

## 🎉 开始使用

现在您已经了解了 OpenRouter 的强大功能，开始享受多模型 AI 测试的便利吧！

```bash
# 1. 设置 API 密钥
echo "OPENROUTER_API_KEY=sk-or-v1-your-key" >> cua-server/.env.development

# 2. 测试设置
node test-ai-models-setup.js

# 3. 启动应用
npm run dev

# 4. 访问 http://localhost:3000 并选择 OpenRouter
```
