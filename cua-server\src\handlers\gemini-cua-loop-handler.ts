// lib/handlers/gemini-cua-loop-handler.ts
import playwright, { <PERSON> } from "playwright";
const { chromium } = playwright;
import logger from "../utils/logger";
import { geminiComputerUseLoop } from "../lib/gemini-computer-use-loop";
import { Socket } from "socket.io";
import GeminiTestScriptReviewAgent from "../agents/gemini-test-script-review-agent";
import { setupGeminiModel } from "../services/gemini-client";
import { LoginService } from "../services/login-service";
import { ModelInput } from "../services/gemini-client";

// Read viewport dimensions from .env file with defaults if not set
const displayWidth: number = parseInt(process.env.DISPLAY_WIDTH || "1024", 10);
const displayHeight: number = parseInt(process.env.DISPLAY_HEIGHT || "768", 10);

export async function handleGeminiCUALoop(
  socket: Socket,
  data: {
    url: string;
    userInstruction: string;
    userInfo: string;
    loginRequired: boolean;
    username?: string;
    password?: string;
  }
) {
  const { url, userInstruction, userInfo, loginRequired, username, password } = data;

  logger.debug("Starting Gemini CUA loop with data:", {
    url,
    userInstruction: userInstruction.substring(0, 100) + "...",
    userInfo: userInfo.substring(0, 100) + "...",
    loginRequired,
    hasCredentials: !!(username && password),
  });

  // Initialize the test script review agent
  const testCaseReviewAgent = new GeminiTestScriptReviewAgent();

  let browser, page: Page;

  try {
    // Launch browser
    browser = await chromium.launch({
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const context = await browser.newContext({
      viewport: { width: displayWidth, height: displayHeight },
    });

    page = await context.newPage();

    // Navigate to the URL
    logger.debug(`Navigating to URL: ${url}`);
    await page.goto(url, { waitUntil: "networkidle" });

    // Handle login if required
    if (loginRequired && username && password) {
      logger.debug("Login required, attempting to log in...");
      const loginService = new LoginService();
      const loginSuccess = await loginService.performLogin(page, username, password);
      
      if (!loginSuccess) {
        socket.emit("message", "❌ Login failed. Please check your credentials.");
        socket.data.testCaseStatus = "fail";
        await browser.close();
        return;
      }
      
      socket.emit("message", "✅ Login successful.");
      
      // Wait a bit for the page to load after login
      await page.waitForTimeout(2000);
    }

    // Take initial screenshot
    const initialScreenshot = await page.screenshot();
    const initialScreenshotBase64 = initialScreenshot.toString("base64");

    // Setup the Gemini model with initial context
    logger.debug("Setting up Gemini model...");
    let response = await setupGeminiModel(userInstruction, userInfo);

    socket.emit("message", "🤖 Gemini model initialized. Starting test execution...");

    // Send the initial screenshot to the model
    const modelInput: ModelInput = {
      screenshotBase64: initialScreenshotBase64,
      previousResponseId: response.id,
    };

    response = await import("../services/gemini-client").then(module => 
      module.sendInputToModel(modelInput, "Please analyze the current screen and start executing the test instructions.")
    );

    // Start the computer use loop
    logger.debug("Starting Gemini computer use loop...");
    await geminiComputerUseLoop(page, response, testCaseReviewAgent, socket);

    logger.debug("Gemini CUA loop completed successfully.");

  } catch (error) {
    logger.error("Error in Gemini CUA loop:", error);
    socket.emit("message", `❌ Error: ${error instanceof Error ? error.message : String(error)}`);
    socket.data.testCaseStatus = "fail";
  } finally {
    // Clean up
    try {
      if (browser) {
        await browser.close();
        logger.debug("Browser closed successfully.");
      }
    } catch (cleanupError) {
      logger.error("Error during cleanup:", cleanupError);
    }
  }
}
