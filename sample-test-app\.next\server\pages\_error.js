const CHUNK_PUBLIC_PATH = "server/pages/_error.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/_7cc58677._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__c75c51b7._.js");
runtime.loadChunk("server/chunks/ssr/0ee23_next_9766003e._.js");
runtime.loadChunk("server/chunks/ssr/_d4e2067b._.js");
runtime.getOrInstantiateRuntimeModule("[project]/sample-test-app/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/sample-test-app/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/sample-test-app/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/sample-test-app/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/sample-test-app/node_modules/next/dist/esm/build/templates/pages.js { INNER_PAGE => \"[project]/sample-test-app/node_modules/next/error.js [ssr] (ecmascript)\", INNER_DOCUMENT => \"[project]/sample-test-app/node_modules/next/document.js [ssr] (ecmascript)\", INNER_APP => \"[project]/sample-test-app/node_modules/next/app.js [ssr] (ecmascript)\" } [ssr] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
