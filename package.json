{"name": "testing-agent-monorepo", "private": true, "workspaces": ["sample-test-app", "frontend", "cua-server"], "scripts": {"dev:sample-test-app": "npm run dev --prefix sample-test-app", "dev:frontend": "npm run dev --prefix frontend", "dev:cua-server": "npm run dev --prefix cua-server", "dev": "concurrently \"npm:dev:sample-test-app\" \"npm:dev:frontend\" \"npm:dev:cua-server\""}, "devDependencies": {"concurrently": "^8.2.2"}, "overrides": {"@types/react": "18.3.20", "@types/react-dom": "18.3.7"}, "dependencies": {"lightningcss": "^1.30.1"}}