import { PROMPT_WITHOUT_LOGIN, PROMPT_WITH_LOGIN } from "../lib/constants";
import logger from "../utils/logger";
import OpenAI from "openai";

export interface TestCaseStep {
  step_number: number;
  step_instructions: string;
  status: string | null;
}

export interface TestCase {
  steps: TestCaseStep[];
}

class OpenRouterTestCaseAgent {
  private readonly client: OpenAI;
  private readonly developer_prompt: string;
  private readonly login_required: boolean;
  private readonly model: string;

  constructor(login_required = false) {
    this.login_required = login_required;
    this.developer_prompt = login_required
      ? PROMPT_WITH_LOGIN
      : PROMPT_WITHOUT_LOGIN;
    
    // Initialize OpenRouter client
    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error("OPENROUTER_API_KEY environment variable is required for OpenRouter agent");
    }

    this.client = new OpenAI({
      apiKey: process.env.OPENROUTER_API_KEY,
      baseURL: "https://openrouter.ai/api/v1",
      defaultHeaders: {
        "HTTP-Referer": process.env.OPENROUTER_SITE_URL || "http://localhost:3000",
        "X-Title": process.env.OPENROUTER_SITE_NAME || "AI Testing Agent",
      },
    });
    
    // Set the default model
    this.model = process.env.OPENROUTER_MODEL || "anthropic/claude-3.5-sonnet";
    
    logger.trace(`Developer prompt: ${this.developer_prompt}`);
    logger.trace(`Using OpenRouter model: ${this.model}`);
  }

  /**
   * Generate structured test steps via the OpenRouter API.
   */
  async invokeResponseAPI(userInstruction: string): Promise<TestCase> {
    logger.debug("Invoking OpenRouter API", { userInstruction, model: this.model });
    
    try {
      const response = await this.client.chat.completions.create({
        model: this.model,
        messages: [
          {
            role: "system",
            content: this.developer_prompt
          },
          {
            role: "user",
            content: userInstruction
          }
        ],
        temperature: 0.1,
        max_tokens: 4000,
        response_format: { type: "json_object" }
      });

      const responseText = response.choices[0]?.message?.content;
      
      if (!responseText) {
        throw new Error("No response content received from OpenRouter API");
      }
      
      logger.debug("OpenRouter API output", { output: responseText });
      
      // Parse the JSON response
      const parsedResponse: TestCase = JSON.parse(responseText);
      
      // Validate the response structure
      if (!parsedResponse.steps || !Array.isArray(parsedResponse.steps)) {
        throw new Error("Invalid response structure: missing or invalid steps array");
      }

      // Validate each step
      for (const step of parsedResponse.steps) {
        if (typeof step.step_number !== "number" || 
            typeof step.step_instructions !== "string") {
          throw new Error(`Invalid step structure: ${JSON.stringify(step)}`);
        }
      }

      return parsedResponse;
    } catch (error) {
      logger.error("Error invoking OpenRouter API:", error);
      throw error;
    }
  }

  /**
   * Get the model name being used
   */
  getModelName(): string {
    return this.model;
  }

  /**
   * Check if login is required for this agent
   */
  isLoginRequired(): boolean {
    return this.login_required;
  }

  /**
   * Get available models for this provider
   */
  static getAvailableModels() {
    return {
      "anthropic/claude-3.5-sonnet": "Claude 3.5 Sonnet (Recommended)",
      "anthropic/claude-3-haiku": "Claude 3 Haiku (Fast)",
      "openai/gpt-4o": "GPT-4o (OpenAI)",
      "openai/gpt-4o-mini": "GPT-4o Mini (Fast & Cheap)",
      "google/gemini-pro": "Gemini Pro (Google)",
      "meta-llama/llama-3.1-70b-instruct": "Llama 3.1 70B",
      "qwen/qwen-2.5-72b-instruct": "Qwen 2.5 72B"
    };
  }
}

export default OpenRouterTestCaseAgent;
