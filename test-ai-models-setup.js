#!/usr/bin/env node

/**
 * Test script to verify AI model setup (Gemini, OpenRouter, OpenAI)
 * Run with: node test-ai-models-setup.js
 */

const { GoogleGenerativeAI } = require("@google/generative-ai");
const OpenAI = require("openai");
require('dotenv').config({ path: './cua-server/.env.development' });

async function testGeminiSetup() {
  console.log("🔍 Testing Gemini Setup...");

  const apiKey = process.env.GOOGLE_API_KEY;
  if (!apiKey || apiKey === 'your-google-api-key-here') {
    console.log("❌ GOOGLE_API_KEY not set or using placeholder value");
    return false;
  }

  console.log("✅ Gemini API key found");

  try {
    const genAI = new GoogleGenerativeAI(apiKey);
    const modelNames = [
      "gemini-2.5-flash-preview-05-20",
      "gemini-2.5-flash",
      "gemini-1.5-flash",
      "gemini-pro"
    ];

    console.log("🔄 Testing Gemini API connection...");
    
    for (const modelName of modelNames) {
      try {
        console.log(`  Trying model: ${modelName}`);
        const model = genAI.getGenerativeModel({ model: modelName });
        const result = await model.generateContent("Hello! Please respond with 'Gemini setup successful!'");
        const response = await result.response;
        const text = response.text();

        console.log("✅ Gemini API connection successful");
        console.log("📝 Response:", text);
        console.log(`🎯 Working model: ${modelName}`);
        return { success: true, model: modelName };
      } catch (modelError) {
        console.log(`  ❌ ${modelName} failed:`, modelError.message);
      }
    }

    throw new Error("All Gemini models failed");
  } catch (error) {
    console.log("❌ Gemini API connection failed:", error.message);
    return false;
  }
}

async function testOpenRouterSetup() {
  console.log("\n🔍 Testing OpenRouter Setup...");

  const apiKey = process.env.OPENROUTER_API_KEY;
  if (!apiKey || apiKey === 'your-openrouter-api-key-here') {
    console.log("❌ OPENROUTER_API_KEY not set or using placeholder value");
    return false;
  }

  console.log("✅ OpenRouter API key found");

  try {
    const client = new OpenAI({
      apiKey: apiKey,
      baseURL: "https://openrouter.ai/api/v1",
      defaultHeaders: {
        "HTTP-Referer": process.env.OPENROUTER_SITE_URL || "http://localhost:3000",
        "X-Title": process.env.OPENROUTER_SITE_NAME || "AI Testing Agent",
      },
    });

    const models = [
      "anthropic/claude-3.5-sonnet",
      "anthropic/claude-3-haiku",
      "openai/gpt-4o-mini",
      "google/gemini-pro"
    ];

    console.log("🔄 Testing OpenRouter API connection...");
    
    for (const modelName of models) {
      try {
        console.log(`  Trying model: ${modelName}`);
        const response = await client.chat.completions.create({
          model: modelName,
          messages: [
            {
              role: "user",
              content: "Hello! Please respond with 'OpenRouter setup successful!'"
            }
          ],
          max_tokens: 50,
          temperature: 0.1
        });

        const text = response.choices[0]?.message?.content;
        console.log("✅ OpenRouter API connection successful");
        console.log("📝 Response:", text);
        console.log(`🎯 Working model: ${modelName}`);
        return { success: true, model: modelName };
      } catch (modelError) {
        console.log(`  ❌ ${modelName} failed:`, modelError.message);
      }
    }

    throw new Error("All OpenRouter models failed");
  } catch (error) {
    console.log("❌ OpenRouter API connection failed:", error.message);
    return false;
  }
}

async function testOpenAISetup() {
  console.log("\n🔍 Testing OpenAI Setup...");

  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey || apiKey === 'your-openai-api-key-here') {
    console.log("❌ OPENAI_API_KEY not set or using placeholder value");
    return false;
  }

  console.log("✅ OpenAI API key found");

  try {
    const client = new OpenAI({
      apiKey: apiKey,
    });

    console.log("🔄 Testing OpenAI API connection...");
    
    const response = await client.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "user",
          content: "Hello! Please respond with 'OpenAI setup successful!'"
        }
      ],
      max_tokens: 50,
      temperature: 0.1
    });

    const text = response.choices[0]?.message?.content;
    console.log("✅ OpenAI API connection successful");
    console.log("📝 Response:", text);
    return { success: true, model: "gpt-4o-mini" };
  } catch (error) {
    console.log("❌ OpenAI API connection failed:", error.message);
    return false;
  }
}

async function main() {
  console.log("🧪 Testing AI Models Setup...\n");

  const results = {
    gemini: await testGeminiSetup(),
    openrouter: await testOpenRouterSetup(),
    openai: await testOpenAISetup()
  };

  console.log("\n📊 Summary:");
  console.log("=" .repeat(50));
  
  const workingProviders = [];
  
  if (results.gemini) {
    console.log("✅ Gemini: Working");
    workingProviders.push("Gemini");
  } else {
    console.log("❌ Gemini: Not working");
  }
  
  if (results.openrouter) {
    console.log("✅ OpenRouter: Working");
    workingProviders.push("OpenRouter");
  } else {
    console.log("❌ OpenRouter: Not working");
  }
  
  if (results.openai) {
    console.log("✅ OpenAI: Working");
    workingProviders.push("OpenAI");
  } else {
    console.log("❌ OpenAI: Not working");
  }

  console.log("\n🎯 Recommendations:");
  if (workingProviders.length === 0) {
    console.log("❌ No AI providers are working. Please check your API keys.");
    console.log("\n🔧 Setup Instructions:");
    console.log("1. Get API keys from:");
    console.log("   - Gemini: https://aistudio.google.com/app/apikey");
    console.log("   - OpenRouter: https://openrouter.ai/keys");
    console.log("   - OpenAI: https://platform.openai.com/api-keys");
    console.log("2. Add them to cua-server/.env.development");
  } else {
    console.log(`✅ ${workingProviders.length} provider(s) working: ${workingProviders.join(", ")}`);
    
    if (results.openrouter) {
      console.log("\n🌟 Recommended: Use OpenRouter for access to multiple models");
    } else if (results.gemini) {
      console.log("\n🌟 Recommended: Use Gemini for good performance and cost");
    } else if (results.openai) {
      console.log("\n🌟 Using OpenAI (legacy support)");
    }
    
    console.log("\n📋 Next steps:");
    console.log("1. Start the applications: npm run dev");
    console.log("2. Visit http://localhost:3000");
    console.log("3. Select your preferred AI provider in the model settings");
  }

  process.exit(workingProviders.length > 0 ? 0 : 1);
}

main().catch(console.error);
