# Testing Agent Demo

[![MIT License](https://img.shields.io/badge/License-MIT-green.svg)](frontend/LICENSE)

This monorepo demonstrates how you can use multiple AI models with computer use capabilities to automate frontend testing. It uses [Playwright](https://playwright.dev) to spin up a browser instance and navigate to the web app to be tested. The AI model then follows the provided test case and executes actions on the interface until the test case is done.

## 🤖 Supported AI Providers

- **🌟 OpenRouter** (Recommended) - Access 50+ models through one API
- **🔥 Google Gemini** - Google's latest models with excellent performance
- **⚡ OpenAI** (Legacy) - Original implementation

## 🚀 Quick Start

### Option 1: OpenRouter (Recommended)
1. Get your API key from [OpenRouter](https://openrouter.ai/keys)
2. Set `OPENROUTER_API_KEY` in your environment files
3. Choose from Claude, GPT, Gemini, Llama models
4. See [OPENROUTER_GUIDE.md](OPENROUTER_GUIDE.md) for details

### Option 2: Google Gemini
1. Get your API key from [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Set `GOOGLE_API_KEY` in your environment files
3. Choose your preferred model (Gemini 2.5 Flash recommended)
4. See [GEMINI_MIGRATION_GUIDE.md](GEMINI_MIGRATION_GUIDE.md) for details

The repo contains three applications that work together:

- **frontend** – Next.js web interface used to configure tests and watch them run.
- **cua-server** – Node service that communicates with AI models and drives Playwright to interact in a browser with the sample app.
- **sample-test-app** – Example e‑commerce site used as an example app to test by the agent.

![screenshot](./screenshot.jpg)

> [!CAUTION]  
> Computer use is in preview. Because the model is still in preview and may be susceptible to exploits and inadvertent mistakes, we discourage trusting it in authenticated environments or for high-stakes tasks.

## How to use

1. **Clone the repository**

   ```bash
   git clone https://github.com/openai/openai-testing-agent-demo
   cd openai-testing-agent-demo
   ```

2. **Prepare environment files**

   Copy the example environment files and set your API keys:

   ```bash
   cp frontend/.env.example frontend/.env.development
   cp cua-server/.env.example cua-server/.env.development
   cp sample-test-app/.env.example sample-test-app/.env.development
   ```

   Then edit `cua-server/.env.development` and add your API keys:

   ```bash
   # For OpenRouter (recommended)
   OPENROUTER_API_KEY=sk-or-v1-your-key-here

   # For Google Gemini
   GOOGLE_API_KEY=your-google-api-key-here

   # For OpenAI (legacy)
   OPENAI_API_KEY=your-openai-api-key-here
   ```

   The sample app also defines demo login credentials, which by default are:

   ```bash
   ADMIN_USERNAME=test_user_name
   ADMIN_PASSWORD=test_password
   ```

   Make sure you add a `sample-test-app/.env.development` file with the example credentials to run the demo.

3. **Test your setup** (Optional but recommended)

   ```bash
   node test-ai-models-setup.js
   ```

4. **Install dependencies**

   ```bash
   npm install
   npx playwright install
   ```

5. **Run all apps**

   ```bash
   npm run dev
   ```

   This will start all three apps:

   - Frontend UI: http://localhost:3000
   - Sample app: http://localhost:3005
   - CUA server: [ws://localhost:8000](http://localhost:8000)

   Navigate to [localhost:3000](http://localhost:3000) to see the frontend UI and run the demo.

6. **Select your AI provider**

   In the frontend interface:
   - Choose "OpenRouter" for access to multiple models
   - Choose "Google Gemini" for Google's models
   - Choose "OpenAI" for legacy support

For details on each app see their READMEs:

- [frontend/README.md](frontend/README.md)
- [cua-server/README.md](cua-server/README.md)
- [sample-test-app/README.md](sample-test-app/README.md)

## Customization

You can use this testing agent with any web app you choose, and update the test case and target URL either in the config UI or in the `frontend/lib/constants.ts` file (default values used in the UI).

`sample-test-app` is only provided as an example to try the demo, and `frontend` as a testing interface. The core logic of the testing agent is in `cua-server`, which is what you might want to bring into your own application.

## Contributing

You are welcome to open issues or submit PRs to improve this app, however, please note that we may not review all suggestions.

## Security Notes

- This project is meant to be used on test environments only.
- Do not use real user data in production.

## License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.
