// lib/handlers/openrouter-cua-loop-handler.ts
import playwright, { Page } from "playwright";
const { chromium } = playwright;
import logger from "../utils/logger";
import { openrouterComputerUseLoop } from "../lib/openrouter-computer-use-loop";
import { Socket } from "socket.io";
import OpenRouterTestScriptReviewAgent from "../agents/openrouter-test-script-review-agent";
import { setupOpenRouterModel } from "../services/openrouter-client";
import { LoginService } from "../services/login-service";
import { ModelInput } from "../services/openrouter-client";

// Read viewport dimensions from .env file with defaults if not set
const displayWidth: number = parseInt(process.env.DISPLAY_WIDTH || "1024", 10);
const displayHeight: number = parseInt(process.env.DISPLAY_HEIGHT || "768", 10);

export async function handleOpenRouterCUALoop(
  socket: Socket,
  data: {
    url: string;
    userInstruction: string;
    userInfo: string;
    loginRequired: boolean;
    username?: string;
    password?: string;
    modelName?: string;
  }
) {
  const { url, userInstruction, userInfo, loginRequired, username, password, modelName } = data;

  logger.debug("Starting OpenRouter CUA loop with data:", {
    url,
    userInstruction: userInstruction.substring(0, 100) + "...",
    userInfo: userInfo.substring(0, 100) + "...",
    loginRequired,
    hasCredentials: !!(username && password),
    modelName: modelName || "default"
  });

  // Initialize the test script review agent
  const testCaseReviewAgent = new OpenRouterTestScriptReviewAgent();

  let browser, page: Page;

  try {
    // Launch browser
    browser = await chromium.launch({
      headless: true,
      args: ["--no-sandbox", "--disable-setuid-sandbox"],
    });

    const context = await browser.newContext({
      viewport: { width: displayWidth, height: displayHeight },
    });

    page = await context.newPage();

    // Navigate to the URL
    logger.debug(`Navigating to URL: ${url}`);
    await page.goto(url, { waitUntil: "networkidle" });

    // Handle login if required
    if (loginRequired && username && password) {
      logger.debug("Login required, attempting to log in...");
      const loginService = new LoginService();
      const loginSuccess = await loginService.performLogin(page, username, password);
      
      if (!loginSuccess) {
        socket.emit("message", "❌ Login failed. Please check your credentials.");
        socket.data.testCaseStatus = "fail";
        await browser.close();
        return;
      }
      
      socket.emit("message", "✅ Login successful.");
      
      // Wait a bit for the page to load after login
      await page.waitForTimeout(2000);
    }

    // Take initial screenshot
    const initialScreenshot = await page.screenshot();
    const initialScreenshotBase64 = initialScreenshot.toString("base64");

    // Setup the OpenRouter model with initial context
    logger.debug("Setting up OpenRouter model...");
    let response = await setupOpenRouterModel(userInstruction, userInfo, modelName);

    socket.emit("message", "🤖 OpenRouter model initialized. Starting test execution...");

    // Send the initial screenshot to the model
    const modelInput: ModelInput = {
      screenshotBase64: initialScreenshotBase64,
      previousResponseId: response.id,
    };

    response = await import("../services/openrouter-client").then(module => 
      module.sendInputToModel(modelInput, "Please analyze the current screen and start executing the test instructions.", modelName)
    );

    // Start the computer use loop
    logger.debug("Starting OpenRouter computer use loop...");
    await openrouterComputerUseLoop(page, response, testCaseReviewAgent, socket);

    logger.debug("OpenRouter CUA loop completed successfully.");

  } catch (error) {
    logger.error("Error in OpenRouter CUA loop:", error);
    socket.emit("message", `❌ Error: ${error instanceof Error ? error.message : String(error)}`);
    socket.data.testCaseStatus = "fail";
  } finally {
    // Clean up
    try {
      if (browser) {
        await browser.close();
        logger.debug("Browser closed successfully.");
      }
    } catch (cleanupError) {
      logger.error("Error during cleanup:", cleanupError);
    }
  }
}
