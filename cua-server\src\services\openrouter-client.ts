import OpenAI from "openai";
import logger from "../utils/logger";

// OpenRouter client using OpenAI SDK with custom base URL
let openrouterClient: OpenAI | null = null;

function getOpenRouterClient(): OpenAI {
  if (!openrouterClient) {
    if (!process.env.OPENROUTER_API_KEY) {
      throw new Error("OPENROUTER_API_KEY environment variable is required for OpenRouter client");
    }
    openrouterClient = new OpenAI({
      apiKey: process.env.OPENROUTER_API_KEY,
      baseURL: "https://openrouter.ai/api/v1",
      defaultHeaders: {
        "HTTP-Referer": process.env.OPENROUTER_SITE_URL || "http://localhost:3000",
        "X-Title": process.env.OPENROUTER_SITE_NAME || "AI Testing Agent",
      },
    });
  }
  return openrouterClient;
}

// Environment specific instructions for the model
const envInstructions = process.env.ENV_SPECIFIC_INSTRUCTIONS || "";

// Helper: Read display dimensions from env
const displayWidth: number = parseInt(process.env.DISPLAY_WIDTH || "1024", 10);
const displayHeight: number = parseInt(process.env.DISPLAY_HEIGHT || "768", 10);

const cuaPrompt = `You are a testing agent. You will be given a list of instructions with steps to test a web application. 
You will need to navigate the web application and perform the actions described in the instructions.
Try to accomplish the provided task in the simplest way possible.
Once you believe your are done with all the tasks required or you are blocked and cannot progress
(for example, you have tried multiple times to accomplish a task but keep getting errors or blocked),
use the mark_done tool to let the user know you have finished the tasks.
You do not need to authenticate on user's behalf, the user will authenticate and your flow starts after that.`;

// Define tools for OpenRouter (using OpenAI function calling format)
const tools = [
  {
    type: "function" as const,
    function: {
      name: "computer_use",
      description: "Use computer to interact with the screen through clicks, typing, and other actions",
      parameters: {
        type: "object",
        properties: {
          action: {
            type: "string",
            enum: ["click", "type", "key", "screenshot", "scroll"],
            description: "The action to perform"
          },
          coordinate: {
            type: "array",
            items: { type: "number" },
            description: "X, Y coordinates for click actions"
          },
          text: {
            type: "string",
            description: "Text to type"
          },
          key: {
            type: "string",
            description: "Key to press (e.g., 'Enter', 'Tab', 'Escape')"
          },
          scroll_direction: {
            type: "string",
            enum: ["up", "down", "left", "right"],
            description: "Direction to scroll"
          }
        },
        required: ["action"]
      }
    }
  },
  {
    type: "function" as const,
    function: {
      name: "mark_done",
      description: "Use this tool to let the user know you have finished the tasks.",
      parameters: {
        type: "object",
        properties: {},
        required: []
      }
    }
  }
];

interface OpenRouterResponse {
  id: string;
  output: Array<any>;
  text?: string;
  functionCalls?: Array<{
    name: string;
    args: any;
  }>;
}

export interface ModelInput {
  screenshotBase64: string;
  previousResponseId?: string;
  lastCallId?: string;
}

// Get the appropriate model based on configuration
function getModelName(modelName?: string): string {
  const defaultModel = process.env.OPENROUTER_MODEL || "anthropic/claude-3.5-sonnet";
  return modelName || defaultModel;
}

// Helper to construct and send a request to the OpenRouter model
async function callOpenRouterModel(
  messages: any[], 
  modelName?: string,
  previousResponseId?: string
): Promise<OpenRouterResponse> {
  logger.trace("Sending request body to OpenRouter model...");

  const client = getOpenRouterClient();
  const selectedModel = getModelName(modelName);
  
  try {
    const response = await client.chat.completions.create({
      model: selectedModel,
      messages: messages,
      tools: tools,
      tool_choice: "auto",
      temperature: 0.1,
      max_tokens: 4000,
    });

    const message = response.choices[0]?.message;
    const text = message?.content || "";
    const toolCalls = message?.tool_calls || [];

    // Convert OpenAI tool calls to our format
    const functionCalls = toolCalls.map(call => ({
      name: call.function.name,
      args: JSON.parse(call.function.arguments)
    }));

    logger.trace("Received response from OpenRouter model.");
    
    return {
      id: response.id || `openrouter-${Date.now()}`,
      output: [response],
      text: text,
      functionCalls: functionCalls
    };
  } catch (error) {
    logger.error("Error calling OpenRouter model:", error);
    throw error;
  }
}

/**
 * Sends input (or screenshot output) to the OpenRouter model.
 * If no lastCallId is provided, it sends an initial query.
 */
export async function sendInputToModel(
  { screenshotBase64, previousResponseId, lastCallId }: ModelInput,
  userMessage?: string,
  modelName?: string
): Promise<OpenRouterResponse> {
  logger.trace("Building image input for OpenRouter model...");
  const messages: any[] = [];

  if (userMessage) {
    messages.push({
      role: "user",
      content: userMessage
    });
  }

  if (lastCallId && screenshotBase64) {
    // This is a follow-up call with a screenshot
    logger.trace(`Adding screenshot to the input with the call ID: ${lastCallId}`);
    
    const imageMessage = {
      role: "user" as const,
      content: [
        {
          type: "image_url",
          image_url: {
            url: `data:image/png;base64,${screenshotBase64}`,
            detail: "high"
          }
        }
      ]
    };

    messages.push(imageMessage);
  }

  return callOpenRouterModel(messages, modelName, previousResponseId);
}

export async function sendFunctionCallOutput(
  callId: string,
  previousResponseId: string,
  outputObj: object = {},
  modelName?: string
): Promise<OpenRouterResponse> {
  const messages = [
    {
      role: "assistant" as const,
      content: null,
      tool_calls: [
        {
          id: callId,
          type: "function" as const,
          function: {
            name: callId.split('-')[0], // Extract function name from callId
            arguments: JSON.stringify({})
          }
        }
      ]
    },
    {
      role: "tool" as const,
      content: JSON.stringify(outputObj),
      tool_call_id: callId
    }
  ];

  return callOpenRouterModel(messages, modelName, previousResponseId);
}

export async function setupOpenRouterModel(
  systemPrompt: string, 
  userInfo: string,
  modelName?: string
): Promise<OpenRouterResponse> {
  logger.trace("Setting up OpenRouter model...");
  
  const openrouter_initiation_prompt = `${cuaPrompt}
      ${
        envInstructions
          ? "Environment specific instructions: " + envInstructions
          : ""
      }
      `;

  logger.trace(`OpenRouter system prompt: ${openrouter_initiation_prompt}`);

  const messages = [
    {
      role: "system" as const,
      content: openrouter_initiation_prompt
    },
    {
      role: "user" as const,
      content: `INSTRUCTIONS:\n${systemPrompt}\n\nUSER INFO:\n${userInfo}`
    }
  ];

  return callOpenRouterModel(messages, modelName);
}

// Export popular model names for configuration
export const OPENROUTER_MODELS = {
  CLAUDE_3_5_SONNET: "anthropic/claude-3.5-sonnet",
  CLAUDE_3_HAIKU: "anthropic/claude-3-haiku",
  GPT_4O: "openai/gpt-4o",
  GPT_4O_MINI: "openai/gpt-4o-mini",
  GEMINI_PRO: "google/gemini-pro",
  LLAMA_3_1_70B: "meta-llama/llama-3.1-70b-instruct",
  QWEN_2_5_72B: "qwen/qwen-2.5-72b-instruct"
} as const;

export type OpenRouterModelName = typeof OPENROUTER_MODELS[keyof typeof OPENROUTER_MODELS];
