# CUA Server

![Google Gemini API](https://img.shields.io/badge/Powered_by-Google_Gemini_API-blue)

A Node.js service that interfaces with Google Gemini models and exposes a Socket.IO WebSocket API used by the frontend.

## Setup

1. Copy the example environment file and add your Google API key:
   ```bash
   cp .env.example .env.development
   # edit .env.development and set GOOGLE_API_KEY
   ```
2. Install dependencies and launch the server:
   ```bash
   npm install
   npx playwright install
   npm run dev   # or npm start
   ```
   The server listens on port `8000` by default. Set `SOCKET_PORT` to change it.

### Environment Variables

- `GOOGLE_API_KEY` – required for calls to Google Gemini models.
- `GEMINI_MODEL` (optional) – which Gemini model to use (default `gemini-2.5-flash-preview-05-20`).
- `SOCKET_PORT` (optional) – WebSocket port (default `8000`).
- `CORS_ORIGIN` (optional) – allowed CORS origin for incoming connections.
