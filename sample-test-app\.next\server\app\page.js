const CHUNK_PUBLIC_PATH = "server/app/page.js";
const runtime = require("../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/0ee23_next_dist_f09a8fa4._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__50a7c09c._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__a3637ec3._.js");
runtime.loadChunk("server/chunks/ssr/_2c8c1811._.js");
runtime.loadChunk("server/chunks/ssr/0ee23_next_dist_client_components_forbidden-error_556e8dd0.js");
runtime.loadChunk("server/chunks/ssr/0ee23_next_dist_client_components_unauthorized-error_88063fe4.js");
runtime.loadChunk("server/chunks/ssr/sample-test-app_a3abfc00._.js");
runtime.getOrInstantiateRuntimeModule("[project]/sample-test-app/.next-internal/server/app/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/sample-test-app/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { MODULE_0 => \"[project]/sample-test-app/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/sample-test-app/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/sample-test-app/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/sample-test-app/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/sample-test-app/app/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/sample-test-app/node_modules/next/dist/esm/build/templates/app-page.js?page=/page { MODULE_0 => \"[project]/sample-test-app/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_1 => \"[project]/sample-test-app/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/sample-test-app/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/sample-test-app/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/sample-test-app/app/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
