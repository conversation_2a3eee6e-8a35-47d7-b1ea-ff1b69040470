import { Socket } from "socket.io";
import logger from "../utils/logger";
import GeminiTestCaseAgent from "../agents/gemini-test-case-agent";
import { convertTestCaseToSteps, TestCase } from "../utils/testCaseUtils";
import { handleGeminiCUALoop } from "./gemini-cua-loop-handler";
import GeminiTestScriptReviewAgent from "../agents/gemini-test-script-review-agent";

export async function handleGeminiTestCaseInitiated(
  socket: Socket,
  data: any
): Promise<void> {
  logger.debug(`Received Gemini testCaseInitiated with data: ${JSON.stringify(data)}`);
  try {
    const { testCase, url, userName, password, userInfo, modelName } = data as {
      testCase: string;
      url: string;
      userName: string;
      password: string;
      userInfo: string;
      loginRequired?: boolean;
      modelName?: string;
    };
    const loginRequired = data.loginRequired ?? true;

    logger.debug(`Login required: ${loginRequired}`);
    logger.debug(`Using Gemini model: ${modelName || 'default'}`);

    socket.emit(
      "message",
      "Received test case - working on creating test script with Gemini..."
    );

    // Create system prompt by combining form inputs.
    const msg = `${testCase} URL: ${url} User Name: ${userName} Password: *********\n USER INFO:\n${userInfo}`;

    const testCaseAgent = new GeminiTestCaseAgent(loginRequired);

    const testCaseResponse = await testCaseAgent.invokeResponseAPI(msg);
    const testCaseJson = JSON.stringify(testCaseResponse);

    // Create a new test case review agent.
    const testCaseReviewAgent = new GeminiTestScriptReviewAgent();

    logger.debug(
      `Invoking Gemini test script review agent - This should only be called once per test script run.`
    );

    let testScriptReviewResponse = await testCaseReviewAgent.instantiateAgent(
      `INSTRUCTIONS:\n${testCaseJson}`
    );
    logger.trace(
      `Test script state initialized: ${JSON.stringify(
        testScriptReviewResponse,
        null,
        2
      )}`
    );

    socket.emit("message", "Gemini test script review agent initialized.");

    // Set the test case review agent in the socket.
    socket.data.testCaseReviewAgent = testCaseReviewAgent;

    logger.debug(`Cleaned test case: ${testCaseJson}`);

    socket.emit("testcases", testCaseJson);
    socket.emit("message", "Task steps created with Gemini.");

    const testScript = convertTestCaseToSteps(testCaseResponse as TestCase);

    logger.debug(`Test script: ${testScript}`);

    // Start the test execution using the provided URL.
    // Pass the test case review agent to the Gemini CUA loop handler.
    await handleGeminiCUALoop(socket, {
      url,
      userInstruction: testScript,
      userInfo,
      loginRequired,
      username: userName,
      password: password,
    });
  } catch (error) {
    logger.error(`Error in handleGeminiTestCaseInitiated: ${error}`);
    socket.emit("message", "Error initiating test case with Gemini.");
  }
}

export type TestCaseStep = {
  step_number: number;
  step_instructions: string;
  status: string | null;
};
