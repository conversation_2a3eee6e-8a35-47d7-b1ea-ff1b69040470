// lib/modules/openrouter-computer-use-loop.ts
import { Page } from "playwright";
import {
  sendInputToModel,
  sendFunctionCallOutput,
} from "../services/openrouter-client";
import { handleModelAction } from "../handlers/action-handler";
import logger from "../utils/logger";
import { Socket } from "socket.io";
import OpenRouterTestScriptReviewAgent from "../agents/openrouter-test-script-review-agent";

// Check the dimensions of the viewport and reset them to the default values if they are not the default values.
const defaultWidth = parseInt(process.env.DISPLAY_WIDTH || "1024", 10);
const defaultHeight = parseInt(process.env.DISPLAY_HEIGHT || "768", 10);

export async function openrouterComputerUseLoop(
  page: Page,
  response: any,
  testCaseReviewAgent: OpenRouterTestScriptReviewAgent,
  socket: Socket,
  switchedToNewTab: boolean = false // <-- Flag to ensure recursion happens only once for a new tab.
) {
  await page.screenshot({ path: "screenshot.png" });
  while (true) {
    // Check if the test case status is 'fail'.
    if (socket.data.testCaseStatus === "fail") {
      logger.debug("Test case failed. Exiting the computer use loop.");
      return response;
    }

    if (socket.data.testCaseStatus === "pass") {
      logger.debug("Test case passed. Exiting the computer use loop.");
      return response;
    }

    // Handle OpenRouter function calls
    if (response.functionCalls && response.functionCalls.length > 0) {
      for (const funcCall of response.functionCalls) {
        if (funcCall.name === "mark_done") {
          response = await sendFunctionCallOutput(
            funcCall.name,
            response.id,
            {
              status: "done",
            }
          );
          socket.emit("message", "✅ Test case finished.");
          socket.data.testCaseStatus = "pass";
          await page.context().browser()?.close();
          return response;
        }

        if (funcCall.name === "computer_use") {
          // Handle computer use action
          const action = funcCall.args;
          
          // Add the previous response id to the socket.data.
          socket.data.previousResponseId = response.id;
          socket.data.lastCallId = `${funcCall.name}-${Date.now()}`;

          // Take a screenshot of the page before the action is executed.
          if (["click"].includes(action?.action)) {
            const screenshotBuffer = await page.screenshot();
            const screenshotBase64 = screenshotBuffer.toString("base64");

            const testScriptReviewResponsePromise =
              testCaseReviewAgent.checkTestScriptStatus(screenshotBase64);
            // Asynchronously emit the test script review response to the socket.
            testScriptReviewResponsePromise
              .then((testScriptReviewResponse) => {
                socket.emit("testscriptupdate", testScriptReviewResponse);
              })
              .catch((error) => {
                logger.error(
                  "Error during test script review: {error: " + error + "}"
                );
                socket.emit("testscriptupdate", {
                  error: "Review processing failed.",
                });
              });
          }

          // Convert OpenRouter action format to Playwright action format
          const playwrightAction = convertOpenRouterActionToPlaywright(action);
          
          // Execute the action in the Playwright page.
          await handleModelAction(page, playwrightAction);

          // Allow some time for UI changes to take effect.
          await page.waitForTimeout(1000);

          // Did this action open a new tab? If so, we need to start a new computer-use-loop with the new page context.
          const pages = page.context().pages();
          if (pages.length > 1 && !switchedToNewTab) {
            // Assume the new tab is the last page.
            const newPage = pages[pages.length - 1];
            logger.debug(
              "New tab detected. Switching context to the new tab (recursion will happen only once)."
            );

            // Continue with your logic using newPage...
            const viewport = newPage.viewportSize();
            logger.trace(
              `Viewport dimensions of new page: ${viewport?.width}, ${viewport?.height}`
            );

            if (
              !viewport ||
              viewport.width !== defaultWidth ||
              viewport.height !== defaultHeight
            ) {
              logger.debug(
                `Resetting viewport size from (${viewport?.width || "undefined"}, ${
                  viewport?.height || "undefined"
                }) to default (${defaultWidth}, ${defaultHeight}).`
              );
              await newPage.setViewportSize({
                width: defaultWidth,
                height: defaultHeight,
              });
            }

            // Take a new screenshot of the new page.
            const screenshotBuffer = await newPage.screenshot();
            const screenshotBase64 = screenshotBuffer.toString("base64");

            // Send the screenshot back as a computer_call_output.
            response = await sendInputToModel({
              screenshotBase64,
              previousResponseId: response.id,
              lastCallId: socket.data.lastCallId,
            });

            logger.info(
              "Recursively calling openrouterComputerUseLoop with new page context."
            );
            logger.trace(`Response: ${JSON.stringify(response, null, 2)}`);

            // Recursively call the computerUseLoop with the new page.
            response = await openrouterComputerUseLoop(
              newPage,
              response,
              testCaseReviewAgent,
              socket,
              true
            );

            return response;
          }

          let screenshotBuffer, screenshotBase64;

          logger.trace("Capturing updated screenshot...");

          screenshotBuffer = await getScreenshotWithRetry(page);
          screenshotBase64 = screenshotBuffer.toString("base64");

          // Send the screenshot back as function response
          response = await sendFunctionCallOutput(
            funcCall.name,
            response.id,
            {
              screenshot: screenshotBase64,
              action_result: "completed"
            }
          );
        }
      }
    } else {
      // No function calls found
      logger.debug("No function calls found. Final output from model:");
      logger.debug(`Response text: ${response.text}`);

      if (response.text) {
        // Check if the response is a message.
        logger.debug("Response is a text message. Continuing conversation.");
        
        // Take a screenshot and continue
        const screenshotBuffer = await getScreenshotWithRetry(page);
        const screenshotBase64 = screenshotBuffer.toString("base64");

        response = await sendInputToModel(
          {
            screenshotBase64,
            previousResponseId: response.id,
            lastCallId: socket.data.lastCallId || `continue-${Date.now()}`,
          },
          "Please continue with the next action based on the current screen."
        );
      } else {
        // If there's no text or function calls, we return the response.
        logger.debug(
          `Response has neither function calls nor text. Returning the response.`
        );
        return response;
      }
    }
  }
}

// Convert OpenRouter action format to the format expected by handleModelAction
function convertOpenRouterActionToPlaywright(openrouterAction: any): any {
  const { action, coordinate, text, key, scroll_direction } = openrouterAction;

  switch (action) {
    case "click":
      return {
        type: "click",
        coordinate: coordinate || [0, 0]
      };
    case "type":
      return {
        type: "type",
        text: text || ""
      };
    case "key":
      return {
        type: "key",
        key: key || "Enter"
      };
    case "scroll":
      return {
        type: "scroll",
        coordinate: coordinate || [500, 400],
        scroll_direction: scroll_direction || "down"
      };
    case "screenshot":
      return {
        type: "screenshot"
      };
    default:
      logger.warn(`Unknown action type: ${action}`);
      return {
        type: "screenshot"
      };
  }
}

async function getScreenshotWithRetry(
  page: Page,
  retries = 3
): Promise<Buffer> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      const screenshot = await page.screenshot();
      return screenshot;
    } catch (error) {
      logger.error(`Attempt ${attempt} - Error capturing screenshot: ${error}`);
      if (attempt === retries) {
        throw error;
      }
      await page.waitForTimeout(2000); // wait 2 seconds before retrying
    }
  }
  throw new Error("Failed to capture screenshot after retries");
}
