#!/usr/bin/env node

/**
 * Simple test script to verify Gemini setup
 * Run with: node test-gemini-setup.js
 */

const { GoogleGenerativeAI } = require("@google/generative-ai");
require('dotenv').config({ path: './cua-server/.env.development' });

async function testGeminiSetup() {
  console.log("🧪 Testing Gemini Setup...\n");

  // Check API key
  const apiKey = process.env.GOOGLE_API_KEY;
  if (!apiKey || apiKey === 'your-google-api-key-here') {
    console.error("❌ GOOGLE_API_KEY not set or still using placeholder value");
    console.log("   Please set your actual Google API key in cua-server/.env.development");
    process.exit(1);
  }

  console.log("✅ API key found");

  // Test API connection
  try {
    const genAI = new GoogleGenerativeAI(apiKey);

    // Try different model names in case the preview model name has changed
    const modelNames = [
      "gemini-2.5-flash-preview-05-20",
      "gemini-2.5-flash",
      "gemini-1.5-flash",
      "gemini-pro"
    ];

    console.log("🔄 Testing API connection...");

    let success = false;
    let workingModel = null;

    for (const modelName of modelNames) {
      try {
        console.log(`  Trying model: ${modelName}`);
        const model = genAI.getGenerativeModel({ model: modelName });
        const result = await model.generateContent("Hello! Please respond with 'Gemini setup successful!'");
        const response = await result.response;
        const text = response.text();

        console.log("✅ API connection successful");
        console.log("📝 Response:", text);
        console.log(`🎯 Working model: ${modelName}`);
        workingModel = modelName;
        success = true;
        break;
      } catch (modelError) {
        console.log(`  ❌ ${modelName} failed:`, modelError.message);
      }
    }

    if (!success) {
      throw new Error("All model attempts failed");
    }

    console.log("\n🎉 Gemini setup is working correctly!");
    if (workingModel !== "gemini-2.5-flash-preview-05-20") {
      console.log(`\n⚠️  Note: Update your .env.development file to use the working model:`);
      console.log(`   GEMINI_MODEL=${workingModel}`);
    }
    console.log("\n📋 Next steps:");
    console.log("   1. Make sure all applications are running");
    console.log("   2. Visit http://localhost:3000");
    console.log("   3. Select 'Google Gemini' in the model settings");

  } catch (error) {
    console.error("❌ API connection failed:", error.message);
    console.log("\n🔧 Troubleshooting:");
    console.log("   1. Check your API key is valid");
    console.log("   2. Ensure you have API quota available");
    console.log("   3. Verify network connectivity");
    process.exit(1);
  }
}

// Check if running directly
if (require.main === module) {
  testGeminiSetup().catch(console.error);
}

module.exports = { testGeminiSetup };
