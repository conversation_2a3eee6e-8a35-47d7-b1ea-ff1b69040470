import { PROMPT_WITHOUT_LOGIN, PROMPT_WITH_LOGIN } from "../lib/constants";
import logger from "../utils/logger";
import { GoogleGenerativeAI, GenerativeModel } from "@google/generative-ai";

export interface TestCaseStep {
  step_number: number;
  step_instructions: string;
  status: string | null;
}

export interface TestCase {
  steps: TestCaseStep[];
}

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY || "");

class GeminiTestCaseAgent {
  private readonly model: GenerativeModel;
  private readonly developer_prompt: string;
  private readonly login_required: boolean;

  constructor(login_required = false) {
    this.login_required = login_required;
    this.developer_prompt = login_required
      ? PROMPT_WITH_LOGIN
      : PROMPT_WITHOUT_LOGIN;
    
    // Set the default model to Gemini 2.5 Flash
    const modelName = process.env.GEMINI_MODEL || "gemini-2.5-flash-preview-05-20";
    this.model = genAI.getGenerativeModel({ 
      model: modelName,
      generationConfig: {
        temperature: 0.1,
        responseMimeType: "application/json",
        responseSchema: {
          type: "object",
          properties: {
            steps: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  step_number: { type: "number" },
                  step_instructions: { type: "string" },
                  status: { 
                    type: ["string", "null"],
                    enum: [null, "pending", "Pass", "Fail"]
                  }
                },
                required: ["step_number", "step_instructions", "status"]
              }
            }
          },
          required: ["steps"]
        }
      }
    });
    
    logger.trace(`Developer prompt: ${this.developer_prompt}`);
  }

  /**
   * Generate structured test steps via the Gemini API.
   */
  async invokeResponseAPI(userInstruction: string): Promise<TestCase> {
    logger.debug("Invoking Gemini API", { userInstruction });
    
    try {
      const result = await this.model.generateContent([
        this.developer_prompt,
        userInstruction
      ]);

      const response = await result.response;
      const responseText = response.text();
      
      logger.debug("Gemini API output", { output: responseText });
      
      // Parse the JSON response
      const parsedResponse: TestCase = JSON.parse(responseText);
      
      // Validate the response structure
      if (!parsedResponse.steps || !Array.isArray(parsedResponse.steps)) {
        throw new Error("Invalid response structure: missing or invalid steps array");
      }

      // Validate each step
      for (const step of parsedResponse.steps) {
        if (typeof step.step_number !== "number" || 
            typeof step.step_instructions !== "string") {
          throw new Error(`Invalid step structure: ${JSON.stringify(step)}`);
        }
      }

      return parsedResponse;
    } catch (error) {
      logger.error("Error invoking Gemini API:", error);
      throw error;
    }
  }

  /**
   * Get the model name being used
   */
  getModelName(): string {
    return this.model.model;
  }

  /**
   * Check if login is required for this agent
   */
  isLoginRequired(): boolean {
    return this.login_required;
  }
}

export default GeminiTestCaseAgent;
